const genderPreferenceLib = require('../lib/gender-preference');
const promptsLib = require('../lib/prompts');
const languageLib = require('../lib/languages');
const OpenaiTransaction = require('../models/openai-transaction');
const PostReport = require('../models/post-report');
const PoseVerification = require('../models/pose-verification');
const AppStoreReviewReply = require('../models/app-store-review-reply');
const InterestApproval = require('../models/interest-approval');
const httpErrors = require('../lib/http-errors');
const constants = require('../lib/constants');
const openaiClient = require('../lib/openai-client');
const claudeApiClient = require('../lib/claude-client');
const togetherClient = require('../lib/together-client');
const personalityLib = require('../lib/personality');
const { translate_frontend } = require('./translate');
const { exclude_gpt4_countries } = require('../lib/exclude-gpt4-countries');
const { isValidPicture } = require('../lib/basic');
const { OpenAI, ClaudeAPI, DeepseekTogetherAI } = require('../lib/prompt');
const replicateClient = require('../lib/replicate-client');
const ImageModeration = require('../models/image-moderation');
const ExtractSocialMediaHandle = require('../models/extract-social-media-handle');
const IsVerificationPhotoSuspicious = require('../models/is-verification-photo-suspicious');
const DetectInstagramSpamInImage = require('../models/detect-instagram-spam-in-image');
const InfinityCopy = require('../models/boo-infinity-copy');
const { prepareProfileDetails, buildExampleBlock } = require('./boo-infinity-copy-helper');

function formatStoredPrompt(prompt, imageUrls) {
  return JSON.stringify({ prompt, imageUrls }, null, 2);
}

function useGpt4(user) {
  return user.os == 'ios' || (user.os == 'android' && !exclude_gpt4_countries.includes(user.signupCountry));
}

function formatQuestion(question) {
  let info = '';
  if (question.title) {
    info = `${info}\nPost Title: ${question.title}`;
  }
  if (question.text) {
    info = `${info}\nPost Body: ${question.text}`;
  }
  if (question.poll) {
    info = `${info}\nPost poll options: ${question.poll.options.map(option => option.text).join('; ')}`;
  }
  return info;
}

function getCommunityGuidelines() {
  return `Here are Boo's community guidelines:
Nudity/Sexual Content
There should be no nudity, partial nudity, sexually explicit material, or sexually suggestive photos or videos.
Harassment
We take this problem seriously. Please do not harass or encourage others to do so in any manner. This includes, but is not limited to, sending unwelcome sexual content, stalking, threats, bullying, and intimidation.
Violence and Physical Harm
Boo does not allow for violent or disturbing material, including threats or calls to violence and aggression. The rules are very strict about physical assaults, coercion, and any other act of violence.
Material that promotes, glorifies, or suggests suicide and self-injury is also prohibited. In these situations, we may take action to aid the user, including providing assistance via crisis resources if necessary.
Hate Speech
It is strictly prohibited to publish content that is malicious against persons or groups based on characteristics such as, but not limited to, race, ethnicity, religious affiliation, disability, gender, age, national origin, sexual orientation or gender identity.
Private Information
Don't put personal or other people's information on the internet. SSNs, passports, passwords, financial information, and unlisted contact information are just a few examples of this type of data. Screenshots of Boo app conversations that make other users look bad or are meant to shame someone are also not allowed.
Spam or Self-Promotion as Primary Purpose
Don't post links or URLs taking to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events.
Prostitution and Trafficking
It is a serious violation of the community to promote or advocate for commercial sexual services, human trafficking, or any other non-consensual sexual acts. It may result in an indefinite permanent ban from Boo.
Scamming
Sharing or asking for financial account details (PayPal, Venmo, Cashapp, bank transfers, etc.), for the purpose of gaining money from others will be banned from Boo.
Impersonation
Do not falsify your identity or pretend to be someone else. This includes parody, fan, and celebrity accounts.
Politics
Boo is not for discussion of politics or divisive political issues. Boo is also not a platform for voicing criticism of political parties, governments, countries, politicians, or world leaders. Boo is for making friends, not enemies. Discussing who is right or wrong or criticism of any party in any international conflict or war is not allowed.
Minors
To use Boo, you must be at least 18 years old. We prohibit images of single children. Make sure to appear in the picture if you're posting photos of your own kids. Please immediately report any profile that includes an unaccompanied minor, suggests harm towards a minor, or features a child in a sexual or suggestive way.
Copyright and Trademark Infringement
If your Boo profile includes any copyrighted or trademarked material that is not yours, don't show it unless you have the appropriate rights.
Illegal Usage
Don't use Boo for unlawful actions. If you were to get arrested for it, it is against the law on Boo.
Meanness or Rudeness
Don't be mean or rude to others, including other Boo users. Making fun of or belittling others is also not allowed.`
}


async function handleQuestionReport(question, reason, explanation, reportedBy) {
  if (typeof reason === 'string') {
    reason = [reason];
  }
  let questionContext = formatQuestion(question);
  let questionHasImage = (question.image && !question.isVideo);
  if (questionHasImage) {
    questionContext = `${questionContext}\nPost Image: [Refer to first image]`;
  }
  if (question.audioTranscription) {
    questionContext = `${questionContext}\nAI Transcript of audio from post: ${question.audioTranscription}`;
  }
  let prompt = `You are a moderator for a social media and dating app called Boo. Your job is to make decisions on posts and comments reported by users of the community, determining whether to ban the post or dismiss the report.

This post was reported by a user.
Hashtags: ${question.hashtags.join(', ')}
${questionContext}
Hint: the poster’s language is ${getLanguageName(question.createdBy?.locale)}. ${question.createdBy?.country ? 'Hint: the poster’s country is ' + question.createdBy.country : ''}

${reason ? 'The person who reported this post said: ' + reason.join(', ') : ''}
${explanation ? 'The person who reported this post provided this explanation: ' + explanation : ''}

${getCommunityGuidelines()}

Given the user's reason for reporting and Boo's community guidelines, either ban or dismiss the reported post and provide an explanation for your decision. Format your response as a json object in the following format: { "ban": true/false, "explanation": explanation }
`;
  let model, provider;
  if (questionHasImage) {
    let client = openaiClient.getOpenaiClient();
    model = 'gpt-4o-mini';
    provider = new OpenAI(client, model);
  } else {
    let client = claudeApiClient.getClaudeApiClient();
    model = 'claude-sonnet-4-20250514';
    provider = new ClaudeAPI(client, model);
  }
  let imageUrls = [];
  if (question.image && !question.isVideo) {
    imageUrls.push(`${constants.IMAGE_DOMAIN}${question.image}`);
  }
  const response = await provider.executePrompt({ prompt, image_urls: imageUrls });
  let cost, output, promptTokens, outputTokens, openaiBan, openaiExplanation, isError, errorMessage, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;

    if (output) {
      try {
        console.log(output);
        // remove markdown formatting before parsing
        const parsed = JSON.parse(output.replace('```json', '').replace('```', ''));
        console.log(parsed);

        // get ban result
        openaiBan = parsed.ban;
        if (openaiBan !== true && openaiBan !== false) {
          isError = true;
          errorMessage = 'parsed output does not contain ban field';
        }

        openaiExplanation = parsed.explanation;
      } catch (err) {
        console.log(err);
        isError = true;
        errorMessage = `json parsing error: ${err.message}`;
      }
    }
  }
  if (errorMessage?.includes('safety system')) {
    openaiBan = true;
    isError = false;
  }
  await PostReport.create({
    reportedUser: question.createdBy?._id,
    reportedBy,
    reason,
    explanation,
    reportedQuestion: question._id,
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
    openaiBan,
    openaiExplanation,
  });
  return openaiBan;
}

async function handleCommentReport(comment, question, parentComment, reason, explanation, reportedBy) {
  if (typeof reason === 'string') {
    reason = [reason];
  }
  let commentHasImage = (comment.image && !comment.isVideo);
  let questionHasImage = (question.image && !question.isVideo);
  let questionImageIndex = commentHasImage ? 'second' : 'first';
  let parentCommentHasImage = (parentComment && parentComment.image && !parentComment.isVideo);
  let parentCommentImageIndex = (commentHasImage && questionHasImage) ? 'third'
                              : (commentHasImage || questionHasImage) ? 'second'
                              : 'first';
  let anyImages = commentHasImage || questionHasImage || parentCommentHasImage;
  let prompt = `You are a moderator for a social media and dating app called Boo. Your job is to make decisions on comments reported by users of the community, determining whether to ban the comment or dismiss the report.

This comment was reported by a user.
Comment: ${comment.text}${commentHasImage ? '\nComment Image: [Refer to first image]' : ''}${comment.audioTranscription ? '\nAI Transcript of audio from comment: '+ comment.audioTranscription : ''}

This was a comment in response to this conversation:
Hashtags: ${question.hashtags.join(', ')}
${formatQuestion(question)}${questionHasImage ? '\nPost Image: [Refer to ' + questionImageIndex + ' image]' : ''}
${parentComment ? 'Parent Comment: ' + parentComment.text : ''}${parentCommentHasImage ? '\nParent Comment Image: [Refer to ' + parentCommentImageIndex + ' image]' : ''}

Hint: the commenter’s language is ${getLanguageName(comment.createdBy.locale)}. ${comment.createdBy.country ? 'Hint: the commenter’s country is ' + comment.createdBy.country : ''}

${reason ? 'The person who reported this comment said: ' + reason.join(', ') : ''}
${explanation ? 'The person who reported this post provided this explanation: ' + explanation : ''}

${getCommunityGuidelines()}

Given the user's reason for reporting and Boo's community guidelines, either ban or dismiss the reported comment and provide an explanation for your decision. Format your response as a json object in the following format: { "ban": true/false, "explanation": explanation }
`;

  let model, provider;
  if (anyImages) {
    let client = openaiClient.getOpenaiClient();
    model = 'gpt-4o-mini';
    provider = new OpenAI(client, model);
  } else {
    let client = claudeApiClient.getClaudeApiClient();
    model = 'claude-sonnet-4-20250514';
    provider = new ClaudeAPI(client, model);
  }

  let imageUrls = [];
  if (commentHasImage) {
    imageUrls.push(`${constants.IMAGE_DOMAIN}${comment.image}`);
  }
  if (questionHasImage) {
    imageUrls.push(`${constants.IMAGE_DOMAIN}${question.image}`);
  }
  if (parentCommentHasImage) {
    imageUrls.push(`${constants.IMAGE_DOMAIN}${parentComment.image}`);
  }
  const response = await provider.executePrompt({ prompt, image_urls: imageUrls });
  let cost, output, promptTokens, outputTokens, openaiBan, openaiExplanation, isError, errorMessage, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;

    if (output) {
      try {
        console.log(output);
        // remove markdown formatting before parsing
        const parsed = JSON.parse(output.replace('```json', '').replace('```', ''));
        console.log(parsed);

        // get ban result
        openaiBan = parsed.ban;
        if (openaiBan !== true && openaiBan !== false) {
          isError = true;
          errorMessage = 'parsed output does not contain ban field';
        }

        openaiExplanation = parsed.explanation;
      } catch (err) {
        console.log(err);
        isError = true;
        errorMessage = `json parsing error: ${err.message}`;
      }
    }
  }
  if (errorMessage?.includes('safety system')) {
    openaiBan = true;
    isError = false;
  }
  await PostReport.create({
    reportedUser: comment.createdBy?._id,
    reportedBy,
    reason,
    explanation,
    reportedComment: comment._id,
    parentQuestion: question._id,
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
    openaiBan,
    openaiExplanation,
  });
  return openaiBan;
}

async function isInterestAppropriate(interest, similarInterests, language) {

  let similarInterestsStr = '';
  if (similarInterests.length > 0) {
    const interestsStr = similarInterests.join(', ');
    similarInterestsStr = `- The interest must not be duplicative of an existing interest.
- The interest must not be a mis-spelling of an existing interest.

Here is the list of existing interests: ${interestsStr}.`
  }

  let languageStr = '';
  if (language) {
    languageStr = `Hint: the language of this interest is: ${languageLib.mapLanguageCodeToName(language)}`
  }

  let prompt = `You are the moderator in charge of approving the creation of new interest categories on the Boo dating and social app.

Rules:
- The interest must not be critical or complaining about Boo or the app.
- The interest must be an intelligible word or string of words that isn't just gibberish.
- The interest must not promote violence, harm, or other illegal behavior.
- The interest must not be disparaging or offensive.
${similarInterestsStr}

Respond with one word and either approve or reject this new interest: ${interest}

${languageStr}`

  const imageUrls = [];
  const model = 'gpt-3.5-turbo-0125';
  const client = openaiClient.getOpenaiClient();
  const openAi = new OpenAI(client, model);
  const response = await openAi.executePrompt({ prompt, image_urls: imageUrls });

  let approved = false;
  let cost, output, promptTokens, outputTokens, isError, errorMessage, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    if (output) {
      approved = output.trim().toLowerCase().includes('approve');
    }
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;
  }
  await InterestApproval.create({
    interestName: interest,
    detectedLanguage: language,
    approved,
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
  });

  return approved;
}

async function generateReplyToReview(text, language, country) {
  const languageHint = language ? `Hint: the reviewer’s language is ${languageLib.mapLanguageCodeToName(language)}.` : '';
  const countryHint = country ? `Hint: the reviewer’s country is ${country}.` : '';
  const prompt = `This is a review on the app store for our dating and social app called Boo. Please respond to this review. Always sincerely thank the reviewer for their feedback. If feedback is negative, sincerely apologize. If the reviewer is referring to a bug in the app, and only if there's a bug, ask them to please send more information and screenshots if <NAME_EMAIL>. Detect what language the review is in and respond in the same language as the review. ${languageHint} ${countryHint} Limit your response to 300 characters. Here is the review: \"${text}\"`
  const client = openaiClient.getOpenaiClient();
  const imageUrls = [];
  const model = 'gpt-4o-mini';
  const openAi = new OpenAI(client, model);
  const response = await openAi.executePrompt({ prompt, image_urls: imageUrls, max_tokens: 256 });
  let reply = '';
  let cost, output, promptTokens, outputTokens, isError, errorMessage, processingTime;
  if (response) {
    reply = response.output;
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;
  }
  await AppStoreReviewReply.create({
    review: text,
    reply,
    language,
    country,
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
  });
  return reply;
}

async function generatePersonalityDescription(params) {
  const { profileName, subcategoryName, mbti, enneagram, zodiac } = params;
  let prompt;
  if (mbti) {
    const avatar = personalityLib.getAvatar(mbti, 'en');
    prompt = `You are writing several paragraphs of text for a section of a website. We have determined that ${profileName} from ${subcategoryName} is an ${mbti}. Communicate this and explain how it manifests in their personality. I will give you $200 if your response is professional and positive about personality typing and does not say how difficult it is to guess, or that these types are not definitive or absolute. YOU MUST NOT mention any other names this type are known as or referred to as, and avoid mentioning the "Myers-Briggs Type Indicator" directly. Avoid filler material, just provide an insightful and valuable analysis. End with a strong statement concluding the response.`;
  }
  else if (enneagram) {
    prompt = `You are writing several paragraphs of text for a section of a website. We have determined that ${profileName} from ${subcategoryName} is an Enneagram ${enneagram}. Communicate this and explain how it manifests in their personality. I will give you $200 if your response is professional and positive about personality typing and does not say how difficult it is to guess, or that these types are not definitive or absolute. End with a strong statement concluding the response.`;
  }
  else if (zodiac) {
    prompt = `You are writing several paragraphs of text for a section of a website. We have determined that ${profileName} from ${subcategoryName} was born on ${zodiac}. Communicate this and explain how it manifests in their personality. I will give you $200 if your response is professional and positive about zodiac typing and does not say how difficult it is to guess, or that these types are not definitive or absolute. End with a strong statement concluding the response.`;
  }
  try {
    console.log(prompt);
    const client = openaiClient.getOpenaiClient();
    const openAi = new OpenAI(client, 'gpt-3.5-turbo-0125');
    const response = await openAi.executePrompt({ prompt, image_urls: [] });
    return response.output;
  } catch (e) {
    console.log(e);
  }
  return '';
}

async function translate(text, languageCode) {
  let prompt = `Translate the following into ${languageLib.mapLanguageCodeToName(languageCode)}, preserving all formatting:\n\n${text}`;
  try {
    console.log(prompt);
    const client = openaiClient.getOpenaiClient();
    const openAi = new OpenAI(client, 'gpt-3.5-turbo-0125');
    const response = await openAi.executePrompt({ prompt, image_urls: [] });
    return response.output;
  } catch (e) {
    console.log(e);
  }
  return '';
}

function getDatingPrefStr(user, otherUser) {
  let datingPref = '';
  if (user && otherUser) {
    let preferences = genderPreferenceLib.getMutualPreferences(user, otherUser);
    if (preferences.includes('dating') && preferences.includes('friends')) {
      datingPref = 'Dating/Friends';
    } else if (preferences.includes('dating')) {
      datingPref = 'Dating';
    } else if (preferences.includes('friends')) {
      datingPref = 'Friends';
    }
  }
  return datingPref;
}

function getLocation(user, forceIncludeLocation) {
  if (!user.location) {
    return '';
  }
  if (user.hideLocation && !forceIncludeLocation) {
    return '';
  }
  if (user.hideCity && !forceIncludeLocation) {
    return `${user.state}, ${user.country}`;
  }
  return `${user.city}, ${user.state}, ${user.country}`;
}

function formatFullBio(user, removeSmoking) {
  const bioSections = [];

  const addBioSection = (label, value) => {
    if (value) bioSections.push(`${label}: ${value}`);
  };

  // Basic Information
  addBioSection('School', user.education);
  addBioSection('Work', user.work);

  // More About User
  const { exercise, educationLevel, drinking, smoking, kids, religion } = user.moreAboutUser || {};
  addBioSection('Exercise', exercise);
  addBioSection('Education Level', educationLevel);
  addBioSection('Drinking', drinking);
  if (!removeSmoking) addBioSection('Smoking', smoking);
  addBioSection('Kids', kids);
  addBioSection('Religion', religion);

  // Languages
  if (user.languages?.length > 0) {
    const languageNames = user.languages.map(langCode => languageLib.mapLanguageCodeToName(langCode)).join(', ');
    addBioSection('Languages', languageNames);
  }

  addBioSection('Profile Bio', user.description);

  // Prompts
  for (const prompt of user.prompts) {
    const promptText = promptsLib.getPromptText(prompt);
    if (promptText) addBioSection(promptText, prompt.answer);
  }

  return bioSections.join('\n').substring(0, 1000);
}

function getUserInfo(user, datingPref, includePersonality, includeLocation, includeInterests, includeFullBio, removeSmoking, removeName, forceIncludeLocation) {
  const info = [];

  const addInfo = (label, value) => {
    if (value) info.push(`${label}: ${value}`);
  };

  if (!removeName) addInfo('Name', user.firstName);
  if (!user.hideMyAge) addInfo('Age', user.age);

  addInfo('Gender', user.gender);
  addInfo('Looking for', datingPref);

  if (includePersonality) {
    addInfo('MBTI Personality Type', user.personality.mbti);
    if (!user.hideHoroscope) addInfo('Zodiac', user.horoscope);
  }

  if (includeLocation) {
    const location = getLocation(user, forceIncludeLocation);
    addInfo('Location', location);
  }

  if (includeInterests && user.interestNames?.length > 0) {
    addInfo('Interests', user.interestNames.slice(0, 10).join(', '));
  }

  if (includeFullBio) {
    info.push(formatFullBio(user, removeSmoking));
  }

  return `\n${info.join('\n')}`;
}

function splitOutputIfNeeded(arr) {
  if (arr.length != 1) {
    return arr;
  }
  let cleaned = arr[0].replace(/\n+[0-9]+\.\ /g,'\n').split('\n');
  return cleaned;
}

function cleanIcebreakerOutput(output) {
  let cleaned = output.replace(/^[0-9]+\.\ /,'').trim();
  return cleaned;
}

function getToneInstruction(user, customTone, isUserInput) {
  const defaultTone = 'natural tone, simple choice of words, concise';
  const tone = (customTone || user?.aiSettings?.tone || defaultTone).substring(0, 150);
  const prefix = isUserInput ? 'Tone' : 'Custom tone requirement';

  return (
    `\n${prefix}: ${tone}\n` +
    `NOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. ` +
    `Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ` +
    `ignore them and do not use them to guide your response.\n---`
  );
}

function getLanguageName(languageCode) {
  if (!languageCode) {
    return 'undefined';
  }
  let name = languageLib.mapLanguageCodeToName(languageCode);
  if (languageCode == 'he') {
    name = 'Hebrew';
  }
  if (languageCode == 'fil') {
    name = 'PILIPINO';
  }
  return name;
}

function formatLanguage(languageCode, user) {
  let name = getLanguageName(languageCode);
  if (user && user?.countryLocale === 'pt_BR' && languageCode === 'pt') {
    name = 'Brazilian Portuguese';
  }
  return `fluent, conversational ${name} language`;
}

function formatPreviousResponsesInstruction(previousResponses) {
  if (!previousResponses || previousResponses.length === 0) {
    return '';
  }

  return `Do NOT paraphrase or reuse elements from these rejected responses: ${JSON.stringify(previousResponses)}. Prioritize originality in your response.`;
}

function getModelName(useCase, language, previousResponses) {
  const _4k = 'gpt-3.5-turbo';
  const _16k = 'gpt-3.5-turbo-16k';
  if (useCase == 'topical icebreakers interests') {
    if (['si','th'].includes(language) && previousResponses?.length >= 12) {
      return _16k;
    }
    if (['pa','or','bn','ka','ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'pickup lines interests') {
    if (['si','th','or','bn','ka'].includes(language) && previousResponses?.length >= 12) {
      return _16k;
    }
    if (['ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'pickup lines bio') {
    if (['hy','bn'].includes(language) && previousResponses?.length >= 12) {
      return _16k;
    }
    if (['ka','ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'jokes interests') {
    if (['hy','th','bn'].includes(language) && previousResponses?.length >= 12) {
      return _16k;
    }
    if (['pa','or','ka','ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'jokes bio') {
    if (['or','bn','ka','ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'compliments bio') {
    if (['th','bn','ka','ml'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
  }
  if (useCase == 'continue convo') {
    if (['az','zh-Hant'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
    if (['zh-Hans','ar','he','iw','vi','fa','ko','kk','ur','uk','ru'].includes(language) && previousResponses?.length >= 4) {
      return _16k;
    }
    if (['el','pa','hi','hy','si','th','or','bn','ka','ml','te'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'analyze sentiment' || useCase == 'analyze intent') {
    if (['fa','ur','uk','pa','si','th','or','bn','ka','ml','te'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'analyze performance') {
    if (['vi','kk','hi','hy','fa','ur','uk','pa','si','th','or','bn','ka','ml','te'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'analyze profile') {
    if (['ml'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'analyze compatibility') {
    if (['th','or','bn','ka','ml'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'social suggest') {
    if (['he','fa','el','hy','si','bn','te'].includes(language) && previousResponses?.length >= 8) {
      return _16k;
    }
    if (['pa','ka','ml'].includes(language) && previousResponses?.length >= 4) {
      return _16k;
    }
    if (['or'].includes(language)) {
      return _16k;
    }
  }
  if (useCase == 'social paraphrase') {
    if (['pa','or','ka'].includes(language)) {
      return _16k;
    }
  }
  return _4k;
}

function isLowTempLanguage(language) {
  return ['kk','bn','ko','iw','he','hi','bg','az','ja','et','cs'].includes(language);
}

const withTimeout = (promise) => {
  // 1 minute timeout
  const millis = 60000;
  const timeout = new Promise((resolve, reject) =>
    setTimeout(
      () => reject(new Error('Timeout')),
      millis));
  return Promise.race([
    promise,
    timeout
  ]);
};

const prepareAiClient = (language, user) => {
  let model, client, apiClient;
  model = 'gpt-4o-mini';
  apiClient = openaiClient.getOpenaiClient();
  client = new OpenAI(apiClient, model);
  return { client, model };
};

async function sendOpenaiRequest(params) {
  const { user, target, prompt, temperature, useCase, useCaseSubtype, useCaseSubtype2, language, nojson, json_array_length = 4 } = params;
  console.log(prompt);
  let isError;
  let response_format = nojson ? undefined : { type: 'json_object' };
  const { client, model } = prepareAiClient(language, user);
  let output, errorMessage, cost, promptTokens, outputTokens, processingTime, promptId, payloadOnError;
  try {
    ({ output, errorMessage, cost, promptTokens, outputTokens, processingTime, promptId, payloadOnError } = await withTimeout(client.executePrompt({ prompt, temperature, response_format, shouldTimeout: true })));
  } catch (err) {
    errorMessage = err.message;
  }

  try {
    if (response_format?.type === 'json_object') {
      const response = JSON.parse(output);

      if (model === 'gpt-4o-mini') {
        const responseArray = response.output;

        if (!Array.isArray(responseArray)) {
          throw new Error('Expected output to be an array');
        }

        if (responseArray.length !== json_array_length) {
          throw new Error(`Output array length (${responseArray.length}) does not match expected length (${json_array_length})`);
        }

        // Check that each element in the array is at least 20 characters long
        responseArray.forEach((element, index) => {
          if (typeof element !== 'string' || element.length < 20) {
            throw new Error(`Element at index ${index} is shorter than 20 characters`);
          }
        });
      }
    }
  } catch (err) {
    errorMessage = errorMessage ? `Invalid JSON response: ${err.message || errorMessage}` : `Invalid JSON response`;
  }

  if (errorMessage) {
    isError = true;
  }

  await OpenaiTransaction.create({
    user: user._id,
    target,
    useCase,
    useCaseSubtype,
    useCaseSubtype2,
    prompt,
    output,
    promptTokens,
    outputTokens,
    outputLanguage: language,
    processingTime,
    isError,
    errorMessage,
    cost,
    model,
    promptId: promptId?.toString(),
    payloadOnError,
  });
  if (errorMessage) {
    throw httpErrors.applicationError(errorMessage);
  }
  user.metrics.openaiCosts += cost;
  await user.save();
  return output;
}

async function getIcebreakers(user, otherUser, outputType, language, contextType, selectedInterests, previousResponses, userInput) {
  let temperature;
  if (outputType === 'topical icebreakers') {
    temperature = isLowTempLanguage(language) ? 1 : 1.2;
  } else if (outputType === 'pickup lines' && contextType === 'interests') {
    temperature = 1;
  } else if (outputType === 'pickup lines' && contextType === 'bio') {
    temperature = 0.5;
  } else if (outputType === 'jokes' && contextType === 'interests') {
    temperature = 1.2;
  } else if (outputType === 'jokes' && contextType === 'bio') {
    temperature = 0.5;
  } else if (outputType === 'compliments' && contextType === 'interests') {
    temperature = 0.7;
  } else if (outputType === 'compliments' && contextType === 'bio') {
    temperature = isLowTempLanguage(language) ? 1 : 1.2;
  }

  const previousResponsesStr = formatPreviousResponsesInstruction(previousResponses);
  const formattedLanguage = formatLanguage(language, user);

  let outputStr = outputType;
  if (outputType === 'pickup lines') {
    outputStr = 'flirty pickup lines';
  }
  if (outputType === 'jokes') {
    outputStr = 'funny one-liner jokes';
  }

  const userInputStr = userInput
    ? `\nCustom requirements:\nWhat I want the message to convey: ${userInput.substring(0, 1000)}`
    : '';

  let prompt = '';
  // Starts contextType interests
  if (contextType === 'interests') {
    let relatedStr = '';
    if (selectedInterests.length > 1) {
      relatedStr = `each of which should refer to TWO OR MORE of their interests: ${selectedInterests.join(', ')},`;
    } else {
      relatedStr = `each of which should refer to their interest: ${selectedInterests.join(', ')},`;
    }

    let finalInstruction = `The output MUST be in ${formattedLanguage} and written to ${otherUser.firstName}`;

    if (outputType === 'jokes') {
      finalInstruction = `Each joke MUST focus on and be integrally related to the interest(s) stated above. The output MUST be in ${formattedLanguage} and written to ${otherUser.firstName}`;
    }

    prompt = `Help me talk with my match on Boo, a personality-based social dating app, in ${formattedLanguage}. Provide 4 ${outputStr} for me to send to my match (${otherUser.firstName}), ${relatedStr} formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---${userInputStr}${getToneInstruction(user, null, userInput?.length)}\n${outputType === 'jokes' ? 'Do not use religion or gender as a subject for jokes.' : ''}\n${previousResponsesStr}\n${finalInstruction} Do not translate names. You MUST remember the Custom Tone.`;
  }

  // Starts contextType bio
  if (contextType === 'bio') {
    let datingPref = getDatingPrefStr(user, otherUser);

    const myInfo = `My Info (for context, do not focus on this):\n${getUserInfo(user, datingPref, false, false, false, false, false, true)}`;
    const theirInfo = `Match's Info:\n${getUserInfo(otherUser, datingPref, false, false, false, true, true)}`;

    let finalInstruction = `Ensure each response includes different ${outputType === 'compliments' ? 'nuances' : 'elements'} from the profile bio. The output MUST be in ${formattedLanguage} and written to ${otherUser.firstName}.`;

    if (outputType === 'jokes') {
      finalInstruction = `Each joke MUST focus on a different detail or nuance of the profile bio and be integrally related to that detail. The output MUST be in ${formattedLanguage} and written to ${otherUser.firstName}.`;
    }

    prompt = `Help me talk with my match on Boo, a personality-based social dating app, in ${formattedLanguage}. Provide 4 ${outputStr} for me to send to my match (${otherUser.firstName}), related to their profile bio, formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---${userInputStr}${getToneInstruction(user, null, userInput?.length)}\n${theirInfo}\n---\n${myInfo}\n---\nDo NOT infer similarities between me and my match: focus only on my match’s info. ${outputType === 'jokes' ? 'Do not use religion or gender as a subject for jokes. Do not merge unrelated jokes with comments about their bio.' : ''}\n${previousResponsesStr}\n${finalInstruction} Do not translate names. You MUST remember the Custom Tone.`;
  }

  const response = await sendOpenaiRequest({
    user,
    target: otherUser._id,
    prompt,
    temperature,
    useCase: 'icebreaker',
    useCaseSubtype: outputType,
    useCaseSubtype2: contextType,
    language,
  });
  const output = JSON.parse(response);
  return splitOutputIfNeeded(output.output).map((x) => cleanIcebreakerOutput(x));
}

async function continueConversation(user, otherUser, language, recentMessages, lastSender, lastMessage, previousResponses, userInput) {
  const includeFullBio = recentMessages.length < 500;
  const datingPref = getDatingPrefStr(user, otherUser);

  const myInfo = `My Info:\n${getUserInfo(user, datingPref, false, true)}`;
  const theirInfo = `Their Info:\n${getUserInfo(otherUser, datingPref, includeFullBio, true, includeFullBio, includeFullBio, includeFullBio)}`;

  const previousResponsesStr = formatPreviousResponsesInstruction(previousResponses);
  const formattedLanguage = formatLanguage(language, user);

  let followInstruction = `Provide 4 options for me to send next to ${otherUser.firstName}, to naturally continue the conversation. Each option should differ significantly from the others, with new elements, humor or questions that take the conversation forward differently. Your response MUST adequately respond to the content of ${otherUser.firstName}’s message (${lastMessage}).`;

  if (lastSender === 'me') {
    followInstruction = `Provide 4 different options for me to send next to ${otherUser.firstName}, to naturally continue the conversation.  Your response MUST follow on smoothly from the final message I sent to ${otherUser.firstName}, without contradicting it or attempting to re-answer the same question. Prioritize ANSWERING any questions from ${otherUser.firstName} that have not been answered. Otherwise, prioritize the topic of my last message, going deeper or into an adjacent topic. DO NOT ask more questions of my match.`;
  }

  const userInputStr = userInput
    ? `\nCustom requirements:\nWhat I want the message to convey: ${userInput.substring(0, 1000)}`
    : '';

  const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in ${formattedLanguage}.` +
  `\n---\n` +
  `Recent chat:\n${recentMessages}` +
  `\n---\n` +
  `The messages are in order from older to newest. Pay attention to who said which line, marked in square brackets.` +
  `\n\n${followInstruction}` +
  `\n---${userInputStr}${getToneInstruction(user, null, userInput?.length)}` +
  `\nContext (assume this is true, even if I’ve previously said something wrong in the chat):` +
  `\n---\n` +
  `${myInfo}` +
  `\n---\n` +
  `${theirInfo}` +
  `\n---\n` +
  `Make sure to cross-check the given information about ${otherUser.firstName} before formulating responses, and avoid asking obvious or naive questions based on their profile. Do not say we are similar if our data is different.` +
  `${previousResponsesStr?.length ? `\n\n${previousResponsesStr}\n\n` : `\n\n`}` +
  `Format your response as a JSON object in the following format: ` +
  `{ output: ["example 1", "example 2", "example 3", "example 4"] }. ` +
  `Do not prefix bullet points, numbers, or headings to each string. ` +
  `Do not include my name at the start of the response and do not translate names. ` +
  `Make the responses written from me to ${otherUser.firstName} (but don’t overuse the name).` +
  `\n\nThe output MUST be in ${formattedLanguage}. You MUST remember the Custom Tone.`;

  const response = await sendOpenaiRequest({
    user,
    target: otherUser._id,
    prompt,
    temperature: isLowTempLanguage(language) ? 0.8 : 1,
    useCase: 'continueConversation',
    language,
  });

  function cleanConversationOutput(output) {
    let cleaned = output.replace(/^[0-9]+\.\ /, '');
    const re = new RegExp(`^"?${user.firstName}:"?`);
    cleaned = cleaned.replace(re, '');
    return cleaned.trim();
  }

  const output = JSON.parse(response);
  return splitOutputIfNeeded(output.output).map((x) => cleanConversationOutput(x));
}

function formatAnalysisOutput(output) {
  // replace single newlines with double newlines
  output = output.replace(/(^|[^\n])\n(?!\n)/g, '$1\n\n');

  // for language 'en' llama 3 is used, remove ** from llama || remove * characters
  output = output.replaceAll('**', '');
  return output;
}

async function chatAnalysis(user, otherUser, outputType, language, recentMessages, userInput) {
  let temperature = 1;
  if (outputType === 'sentiment') {
    temperature = isLowTempLanguage(language) ? 0.8 : 1;
  }

  let datingPref = getDatingPrefStr(user, otherUser);
  if (datingPref) {
    datingPref = `We are both looking for: ${datingPref}.`;
  }

  let option, specialInstructions;
  const toneInstruction = userInput ? `\n\nWhat I specifically want to know: ${userInput.substring(0, 1000)}\n` : '';

  if (outputType === 'sentiment') {
    option = 'my match’s sentiment';
    specialInstructions = `Do not assume the conversation takes turns. Each message is correctly labeled with the sender. Do not repeat entire messages or refer to them by a number or ordinal number; instead, use contextual clues or mention the main topic of the message your insight is referring to.\n\nAnalyze ONLY the sentiment behind my match's messages (analyze). Are there signs of interest, happiness, or any other emotions or attitudes? Help me read between the lines. Do not consider or analyze the responses from me (context).\n\nProvide bullet-point insights and a conclusion about the overall sentiment from my match.`;
  } else if (outputType === 'performance') {
    option = 'my rizz game';
    const objective = datingPref.includes('Dating') ? 'flirting' : 'building a friendship';
    specialInstructions = `Do not assume the conversation takes turns. Each message is correctly labeled with the sender and is in order. Do not infer or assume messages that aren't mentioned here.\n\nFocus on my messages (analyze), taking into account my match’s reactions (context) to assess how they respond and whether we’re vibing. Based on this, provide bullet-point insights into my performance at being a great conversation partner, and a conclusion on how well I am ${objective}.`;
  } else if (outputType === 'intent') {
    option = 'my match’s intent';
    specialInstructions = `Do not assume the conversation takes turns. Each message is correctly labeled with the sender. Do not repeat entire messages or refer to them by a number or ordinal number; instead, use contextual clues or mention the main topic of the message your insight is referring to.\n\nProvide bullet-point insights into the OVERALL intent behind my match’s messages (analyze) and a conclusion rating how interested my match is in me, but do not assess my messages (context).`;
  }

  const formattedLanguage = formatLanguage(language, user);

  const prompt = `Help me analyze ${option} on Boo, a personality-based social dating app, in ${formattedLanguage}. Here is the most recent section of a longer conversation between me and my match: ${otherUser.firstName}. Their data: Age: ${otherUser.age}, Gender: ${otherUser.gender}. ${datingPref}${toneInstruction}\n---\nRecent chat:\n${recentMessages}\n---\n${specialInstructions}\nDo not list or translate elements of this prompt in your output, including names, profile data or chat.\nThe output MUST be in ${formattedLanguage}, addressing me in the SECOND person.`;

  const response = await sendOpenaiRequest({
    user,
    target: otherUser._id,
    prompt,
    temperature,
    useCase: 'chatAnalysis',
    useCaseSubtype: outputType,
    language,
    nojson: true,
  });
  return formatAnalysisOutput(response);
}

async function profileAnalysis(user, otherUser, outputType, language, userInput) {
  let datingPref = getDatingPrefStr(user, otherUser);
  let option, specialInstructions;
  const toneInstruction = userInput ? `\n\nWhat I specifically want to know: ${userInput.substring(0, 1000)}\n` : '';

  if (outputType === 'profile') {
    option = 'my match’s profile';
    specialInstructions = 'offering 4 concise and tailored insights into their likes, dislikes, and how to best connect with them, based on their 16-Type as well as their interests and bio';
  } else if (outputType === 'compatibility') {
    option = 'my compatibility with my match';
    specialInstructions = 'and do not repeat sections of our info in your response, but focus on how these details and our 16-Types affect our compatibility';
  }

  let myInfo = '';
  if (outputType === 'compatibility') {
    myInfo = `My Info:\n${getUserInfo(user, datingPref, true, true, true, true, false, true, true)}`;
  }
  const theirInfo = `Their Info:\n${getUserInfo(otherUser, datingPref, true, true, true, true)}`;
  const formattedLanguage = formatLanguage(language, user);

  const prompt = `Help me analyze ${option} on Boo, a personality-based social dating app, in ${formattedLanguage}. Provide your insights as bullet points, ${specialInstructions}.${toneInstruction}\n---\n${myInfo}\n---\n${theirInfo}\n---\nDo not list or translate elements of this prompt in your output, including names and profile data.\nThe output MUST be in ${formattedLanguage}, addressing me in the SECOND person.`;

  const response = await sendOpenaiRequest({
    user,
    target: otherUser._id,
    prompt,
    temperature: isLowTempLanguage(language) ? 0.8 : 1,
    useCase: 'profileAnalysis',
    useCaseSubtype: outputType,
    language,
    nojson: true,
  });
  return formatAnalysisOutput(response);
}

async function processSocial(user, outputType, language, userInput, question, comment, previousResponses) {
  let temperature = 1;
  if (outputType === 'suggest') {
    temperature = isLowTempLanguage(language) ? 0.6 : 0.8;
  }

  const _creatorDetails = (creator) => {
    if (creator && !creator.banned) {
      return { firstName: creator.firstName, gender: creator.gender, anonymousProfileNickname: creator.anonymousProfileNickname };
    }
    return { firstName: '', gender: '', anonymousProfileNickname: ''};
  };

  const _userBeingRepliedTo = (comment, question) => {
    if (comment) {
      return comment.postedAnonymously
        ? comment.createdBy?.anonymousProfileNickname
        : comment.createdBy?.firstName;
    }

    if (question) {
      const creatorDetails = _creatorDetails(question.createdBy);
      return question.postedAnonymously
        ? creatorDetails.anonymousProfileNickname
        : creatorDetails.firstName;
    }

    return ''; // Fallback if neither comment nor question is provided
  };

  userInput = userInput.substring(0, 1000);

  const userBeingRepliedTo = _userBeingRepliedTo(comment, question) || '';
  const replyToInstruction = userBeingRepliedTo ? `, from me (${user.firstName}) to ${userBeingRepliedTo}` : '';
  const universeString = `Universe: ${question.interestName}`;
  const questionCreator = _creatorDetails(question.createdBy);

  const postContext = questionCreator.firstName
    ? `Post by ${question.postedAnonymously ? questionCreator.anonymousProfileNickname : questionCreator.firstName} ${questionCreator.gender}`
    : 'Post';
  const postString = `|| ${postContext}: ${question.title ? `${question.title} ` : ''}${question.text.substring(0, 1000)}`;

  let commentString;
  let context = `${universeString}\n${postString}`;
  if (comment) {
    const commentBy = `|| Comment by ${comment.postedAnonymously ? comment.createdBy?.anonymousProfileNickname : comment.createdBy?.firstName}`;
    commentString = `${commentBy} (${comment.createdBy?.gender}): ${comment.text.substring(0, 1000)}`;
    context = `${context}\n${commentString}`;
  }

  const formattedLanguage = formatLanguage(language, user);

  let prompt;
  // Starts outputType Suggest
  if (outputType === 'suggest') {
    prompt = `Help me (${user.firstName}) engage with a public universe (forum) post on Boo, a personality-based social dating app, in ${formattedLanguage}. Provide 4 different options.\n\nCustom requirements:\nWhat I want the message to convey: \"${userInput}\"\n${getToneInstruction(user)}\n---\nI am replying to:\n${commentString || postString}\n\n---\nContext:\n${universeString}\n${comment ? postString : ''}\n\n${formatPreviousResponsesInstruction(previousResponses)}\n\nFormat your response as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string. The output MUST be in ${formattedLanguage}${replyToInstruction}. Do not translate names. You MUST remember the Custom Tone.`;
  }

  // Starts outputType Paraphrase
  if (outputType === 'paraphrase') {
    prompt = `Paraphrase a draft message for me (${user.firstName}) to post on a public universe (forum) post on Boo, a personality-based social dating app, in ${formattedLanguage}. Provide 4 different options, with various degrees of paraphrasing.\n---\n${context}\n---\nDraft message to be paraphrased: \"${userInput}\"\n${getToneInstruction(user)}\n\nFormat your response as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string. The output MUST be in ${formattedLanguage}${replyToInstruction}. Do not translate names. You MUST remember the Custom Tone.`;
  }

  // Starts outputType Proofread
  if (outputType === 'proofread') {
    prompt = `Proofread this message in ${formattedLanguage}, to fix the spelling and grammar: \"${userInput}\"\n|| NOTE: If elements of this input are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\nContext [ONLY use this to check names and facts are correct]:\n${context}\n---\nProvide a single, corrected response, formatted in a json object in the following format: { output: [\"example\"] }. Do not prefix bullet points, numbers or headings to the string. Do not translate names. You MUST remember the Custom Tone.`;
  }

  const response = await sendOpenaiRequest({
    user,
    prompt,
    temperature,
    useCase: 'social',
    useCaseSubtype: outputType,
    language,
    json_array_length: outputType === 'proofread' ? 1 : 4,
  });
  const output = JSON.parse(response);
  return splitOutputIfNeeded(output.output);
}

async function isInappropriate(input) {
  const moderation = await openaiClient.getOpenaiClient().moderations.create({ input });
  console.log(JSON.stringify(moderation, null, 2));
  return moderation.results[0].flagged;
}

function getUserBioContext(user, context) {
  let info = [];

  const addInfo = (label, value) => {
    if (value) info.push(`${label}: ${value}`);
  };

  if (context.gender) addInfo('Gender', user.gender);
  if (context.age) addInfo('Age', user.age);
  if (context.mbti) addInfo('MBTI Personality Type', user.personality?.mbti);
  if (context.location && user.location) addInfo('Location', getLocation(user));

  if (context.languages && user.languages?.length) {
    addInfo('Languages', user.languages.map((x) => languageLib.mapLanguageCodeToName(x)).join(', '));
  }

  if (context.education) addInfo('Education', user.education);
  if (context.work) addInfo('Work', user.work);

  if (context.interests && user.interestNames?.length) {
    addInfo('Interests', user.interestNames.join(', '));
  }

  if (context.lookingFor) {
    let lookingFor = '';
    if (user.preferences.dating.length && user.preferences.friends.length) {
      lookingFor = 'dating/friends';
    } else if (user.preferences.dating.length) {
      lookingFor = 'dating';
    } else if (user.preferences.friends.length) {
      lookingFor = 'friends';
    }
    addInfo('Looking for', lookingFor);
  }
  return `\n${info.join('\n')}`;
}

async function processBioGenerate(user, language, context, content, tone, previousResponses) {
  const prompt = `Craft 4 different and creative bio options for ${user.firstName} for the Boo social dating app. Each bio should offer a unique tone of voice and highlight different facets of ${user.firstName}’s life. Avoid using a list-like format and prioritize fluency, creativity and appeal.\n\nMAIN FOCUS (prioritize this in the bio): ${content}\n\n${getToneInstruction(user, tone)}\n---\n${user.firstName}‘s data (for context, allude to some of it naturally):\n${getUserBioContext(user, context)}\n---\n\nFormat the output as a json object: { output: [\"bio 1\", \"bio 2\", \"bio 3\", \"bio 4\"]}.\nThe output must be in ${formatLanguage(language, user)}.\n\n${formatPreviousResponsesInstruction(previousResponses)}. You MUST remember the Custom Tone.`;

  const response = await sendOpenaiRequest({
    user,
    prompt,
    useCase: 'bio',
    useCaseSubtype: 'generate',
    language,
  });
  const output = JSON.parse(response);
  return output.output;
}

async function processBioImprove(user, language, bio, context, content, previousResponses) {
  const prompt = `Improve ${user.firstName}'s bio for the Boo social dating app. Craft 4 different and creative bio options that each offer a unique tone of voice and highlight different facets of ${user.firstName}'s life. Avoid using a list-like format and prioritize fluency, creativity and appeal. Here is the current bio:\n---\n${bio}\n---\nMAIN FOCUS (prioritize this in the bio): ${content}\n---\n${user.firstName}‘s data (for context, allude to some of it naturally):\n${getUserBioContext(user, context)}\n---\nFormat the output as a json object: { output: [\"bio 1\", \"bio 2\", \"bio 3\", \"bio 4\"]}.\nThe output must be in ${formatLanguage(language, user)}.\n\n${formatPreviousResponsesInstruction(previousResponses)}`;

  const response = await sendOpenaiRequest({
    user,
    prompt,
    useCase: 'bio',
    useCaseSubtype: 'improve',
    language,
  });
  const output = JSON.parse(response);
  return output.output;
}

async function processBioChangeTone(user, language, bio, tone, previousResponses) {
  const prompt = `Rewrite ${user.firstName}'s bio for the Boo social dating app, by changing the tone of voice. Avoid using a list-like format and prioritize fluency, creativity and appeal. Here is the current bio:\n---\n${bio}\n---\n${getToneInstruction(user, tone)}\n---\nFormat the output as a json object: { output: [\"bio 1\", \"bio 2\", \"bio 3\", \"bio 4\"]}.\nThe output must be in ${formatLanguage(language, user)}.\n\n${formatPreviousResponsesInstruction(previousResponses)}. You MUST remember the Custom Tone.`;

  const response = await sendOpenaiRequest({
    user,
    prompt,
    useCase: 'bio',
    useCaseSubtype: 'changeTone',
    language,
  });
  const output = JSON.parse(response);
  return output.output;
}

async function processBioProofread(user, language, bio, previousResponses) {
  const prompt = `Proofread ${user.firstName}'s bio on Boo social dating app, in ${formatLanguage(language, user)} language, to fix the spelling and grammar. Here is the current bio:\n---\n${bio}\n---\nProvide a single, corrected response, formatted in a json object in the following format: { output: [\"bio\"] }. Do not prefix bullet points, numbers or headings to the string. Do not translate names.\n\n${formatPreviousResponsesInstruction(previousResponses)}`;

  const response = await sendOpenaiRequest({
    user,
    prompt,
    useCase: 'bio',
    useCaseSubtype: 'proofread',
    language,
    json_array_length: 1,
  });
  const output = JSON.parse(response);
  return output.output;
}

function formatProfileDataForReports(user) {
  let bio = `Name: ${user.firstName}
Self-reported Age: ${user.age}
Previous or Current Education Level or Institution (tip: saying "high school" or "middle school" doesn't mean they're underage, but may be indicating that was their highest level of education attainment): ${user.education}
Work: ${user.work}
Bio: ${user.description}`;
  if (user.audioDescriptionTranscription) {
    bio = `${bio}\nAI Transcript of audio from profile: ${user.audioDescriptionTranscription}`;
  }
  for (const prompt of user.prompts) {
    const promptText = promptsLib.getPromptText(prompt);
    if (promptText) {
      bio = `${bio}\n${promptText}: ${prompt.answer}`;
    }
  }
  return bio;
}

function hasProfilePictures(user) {
  const content = [];
  appendProfilePicturesToContent(user, content);
  return content.length > 0;
}

function countProfilePictures(user) {
  const content = [];
  appendProfilePicturesToContent(user, content);
  return content.length;
}

function appendProfilePicturesToContent(user, content) {
  for (const picture of user.pictures) {
    if (isValidPicture(picture)) {
      content.push({
        type: 'image_url',
        image_url: {
          url: `${constants.IMAGE_DOMAIN}${picture}`,
          detail: 'low',
        },
      });
    }
  }
}

function formatMessages(messages, reportedUser, pictureIndex = 0) {
  let result = 'Messages:';
  for (const message of messages) {
    const sender = (message.sender == reportedUser._id) ? 'Reported User' : 'Reporter';
    if (message.image && isValidPicture(message.image)) {
      result = `${result}\n${sender}: [see attached picture ${++pictureIndex} (assume 1-based indexing)]`;
    }
    else if (message.text) {
      result = `${result}\n${sender}: ${message.text}`;
    }
  }
  return result;
}

function appendMessagePicturesToContent(messages, content) {
  for (const message of messages) {
    if (message.image && isValidPicture(message.image)) {
      content.push({
        type: 'image_url',
        image_url: {
          url: `${constants.IMAGE_DOMAIN}${message.image}`,
          detail: 'low',
        },
      });
    }
  }
}


function getCommunityGuidelinesForUserProfileReports() {
  return `
1. Nudity/Sexual Content.
    a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.
2. Hate Speech
3. Spam, Promotion or Solicitation
    a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username). Additionally, check for attempts to circumvent detection by separating characters in the social media handle with spaces or special characters, such as “c i a r r a w h i s p p e r” or “c_i_a_r_r_a_w_h_i_s_p_p_e_r”.
4. Prostitution and Trafficking
    a. Selling sexual services.
5. Scamming
    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.
6. Underage (below age 18)
    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're "18" or "19", but in their bio they say things like "17", "actually I'm 17", "17 not 18". If a user only includes a number on their profile without other context like "17" or "17 years", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. "my kid is 12", "I have a 17 year old". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.
`
}

const processReportResponse = async (report, prompt, imageUrls, provider, model, violationLocation, response, otherParams = {}) => {
  let cost, output, reasoning_content, parsed, promptTokens, outputTokens, ban, banReason, decision, infringingText, infringingPictures, infringingPictureKeys, isError, errorMessage, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    reasoning_content = response.reasoning_content;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;
    if (output) {
      try {
        console.log(output);
        // remove markdown formatting before parsing
        parsed = JSON.parse(output.replace('```json', '').replace('```', '').trim());
        console.log(parsed);

        // get ban result
        ban = parsed.ban;
        if (ban !== true && ban !== false) {
          isError = true;
          errorMessage = 'parsed output does not contain ban field';
        }

        if (ban) {
          // get ban reason
          if (!parsed.reason) {
            isError = true;
            errorMessage = 'parsed output does not contain reason field';
          } else {
            const lower = parsed.reason.trim().toLowerCase();
            if (lower.includes('underage')) {
              banReason = 'Underage';
            } else if (lower.includes('scam')) {
              banReason = 'Scamming';
            } else if (lower.includes('nudity') || lower.includes('sex')) {
              banReason = 'Nudity/Sexual Content';
            } else if (lower.includes('harass')) {
              banReason = 'Harassment';
            } else if (lower.includes('violence') || lower.includes('harm')) {
              banReason = 'Violence and Physical Harm';
            } else if (lower.includes('hate') || lower.includes('speech')) {
              banReason = 'Hate Speech';
            } else if (lower.includes('spam') || lower.includes('promot') || lower.includes('solicit')) {
              banReason = 'Spam, Promotion or Solicitation';
            } else if (lower.includes('prostitut') || lower.includes('traffick') || lower.includes('traffic')) {
              banReason = 'Prostitution and Trafficking';
            } else if (lower.includes('impersonat')) {
              banReason = 'Impersonation';
            } else if (lower.includes('private') || lower.includes('sensitive')) {
              banReason = 'Revealing another person’s private or sensitive information';
            } else if (lower.includes('rude') || lower.includes('mean')) {
              banReason = 'Meanness or Rudeness';
            }
          }

          // get violation location
          if (violationLocation !== 'profile' && violationLocation !== 'messages') {
            if (!parsed.violationLocation) {
              isError = true;
              errorMessage = 'parsed output does not contain violationLocation field';
            } else {
              const lower = parsed.violationLocation.trim().toLowerCase();
              if (lower.includes('profile')) {
                violationLocation = 'profile';
              } else if (lower.includes('message')) {
                violationLocation = 'messages';
              }
            }
          }

          // get infringing text and pictures
          if (parsed.infringingText && Array.isArray(parsed.infringingText) && parsed.infringingText.length && parsed.infringingText.every(x => typeof x === 'string')) {
            infringingText = parsed.infringingText.filter(x => x !== 'undefined');
            if (!infringingText.length) {
              infringingText = undefined;
            }
          }
          if (parsed.infringingPictures && Array.isArray(parsed.infringingPictures) && parsed.infringingPictures.length && parsed.infringingPictures.every(Number.isInteger)) {
            infringingPictures = parsed.infringingPictures;
            infringingPictureKeys = parsed.infringingPictures
              .map(index => imageUrls[index]?.replace(constants.IMAGE_DOMAIN, ''))
              .filter(Boolean);
          }
        }
      } catch (err) {
        console.log(err);
        isError = true;
        errorMessage = `json parsing error: ${err.message}`;
      }
    }
  }

  if (errorMessage?.includes('safety system')) {
    ban = true;
    banReason = 'Nudity/Sexual Content';
    isError = false;
  }

  // get decision
  if (ban) {
    if (['Underage', 'Scamming', 'Prostitution and Trafficking'].includes(banReason)) {
      decision = 'shadow_ban';
    } else if (violationLocation === 'profile') {
      if (['Spam, Promotion or Solicitation', 'Hate Speech'].includes(banReason)) {
        decision = 'shadow_hide';
      } else {
        decision = 'temp_shadow_ban';
      }
    } else {
      decision = 'temp_ban';
    }
  } else {
    decision = 'dismiss';
  }

  let socialMediaHandles;
  if (otherParams.isPreemptiveModeration) {
    if (banReason == 'Spam, Promotion or Solicitation' && (infringingText || infringingPictures)) {
      decision = 'shadow_hide';
      if (infringingText) {
        socialMediaHandles = await extractSocialMediaHandles(infringingText.join(' '));
      }
    } else {
      decision = 'dismiss';
    }
  }

  report.openai = {
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    reasoning_content,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    provider,
    model,
    processingTime,
    ban,
    banReason,
    violationLocation,
    decision,
    infringingText,
    infringingPictures,
    infringingPictureKeys,
    socialMediaHandles,
  };
};

// This function is used when any user report another user
const executeUserReportPrompt = async (report, prompt, imageUrls, handleWithGPT, violationLocation, otherParams = {}) => {
  let providerName, model, provider;
  let flaggedImages = [];
  let executablePrompt = prompt;

  // If there is any image which is tagged by hive as yes_overlay_text, then this prompt will be used. This prompt only reviews the specific photo with an overlay text, and not the entire profile. Previous prompt reviews the entire profile is oversensitive to a lack of information in the profile.

  const updatedPromptForOverlayText = `You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate. identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:\n\n\n1. Nudity/Sexual Content.\n a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.\n2. Hate Speech\n3. Spam, Promotion or Solicitation\n a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username).\n4. Prostitution and Trafficking\n a. Selling sexual services.\n5. Scamming\n a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.\n6. Underage (below age 18)\n a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're \"18\" or \"19\", but in their bio they say things like \"17\", \"actually I'm 17\", \"17 not 18\". If a user only includes a number on their profile without other context like \"17\" or \"17 years\", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. \"my kid is 12\", \"I have a 17 year old\". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don't make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.\n\n\nIf any of the above, the user should be banned.\n\nThe person (Reporter) who reported this user (Reported User) said: Catfish/Scammer, Inappropriate Profile, \nWe don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth.\n\nIf the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).\nFormat your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.`;

  const initializeProvider = () => {
    let client, ProviderClass;

    switch (providerName) {
    case 'openai':
      client = openaiClient.getOpenaiClient();
      ProviderClass = OpenAI;
      break;
    case 'together':
      client = togetherClient.getTogetherAiClient();
      ProviderClass = DeepseekTogetherAI;
      break;
    case 'anthropic':
      client = claudeApiClient.getClaudeApiClient();
      ProviderClass = ClaudeAPI;
      break;
    default:
      throw new Error(`Provider ${providerName} not found`);
    }

    return new ProviderClass(client, model);
  };

  const determineProvider = async () => {
    if (otherParams.model == 'gpt-4o-mini') {
      model = 'gpt-4o-mini';
      providerName = 'openai';
      return;
    }
    if (otherParams.model == 'claude-sonnet-4-20250514') {
      model = 'claude-sonnet-4-20250514';
      providerName = 'anthropic';
      return;
    }
    if (otherParams.model == 'deepseek-ai/DeepSeek-R1') {
      model = 'deepseek-ai/DeepSeek-R1';
      providerName = 'together';
      return;
    }

    if (imageUrls?.length > 0) {
      if (handleWithGPT) {
        providerName = 'openai';
        model = 'gpt-4o-mini';
        flaggedImages = imageUrls;
        return;
      }

      // Check from moderated images
      const moderationResults = await ImageModeration.find({
        url: { $in: imageUrls },
        serviceName: 'Hive',
      });

      // checking for images with yes_overlay_text or text tag
      flaggedImages = moderationResults?.filter((result) => result?.moderationLabels?.some((label) => ['yes_overlay_text', 'text'].includes(label?.Name))).map((result) => result?.url);

      if (flaggedImages.length > 0) {
        providerName = 'openai';
        model = 'gpt-4o-mini';
        executablePrompt = updatedPromptForOverlayText; // Use updated prompt
        return;
      }
    }

    // If this is the first report within 48 hours and no image has yes_overlay_text tag or no image for this user(existing logic), then use claude sonnet with text only prompt
    providerName = 'anthropic';
    model = 'claude-sonnet-4-20250514';
  };

  await determineProvider();
  provider = initializeProvider(providerName, model);

  // Execute the prompt
  const response = await provider.executePrompt({
    prompt: executablePrompt,
    image_urls: flaggedImages,
  });

  await processReportResponse(report, executablePrompt, flaggedImages, providerName, model, violationLocation, response, otherParams);
};

// This function is being used to handle auto reports. Use case: Generic auto report, Auto report due to profile keywords, Profile review when updates profile information like first name, description, education, work, etc.
async function executeAutoReportPrompt(report, prompt, imageUrls, violationLocation, otherParams = {}) {
  let providerName, model, provider;

  if (otherParams.isPreemptiveModeration) {
    // claude-sonnet is more accurate than gpt-4o-mini but more expensive,
    // so limit its usage to web users where the risk of spam is higher
    if (otherParams.signupSource == 'web') {
      model = 'claude-sonnet-4-20250514';
    } else {
      model = 'gpt-4o-mini';
    }
  } else {
    if (!imageUrls || !imageUrls.length) {
      model = 'claude-sonnet-4-20250514';
    } else {
      model = 'gpt-4o-mini';
    }
  }

  if (model == 'claude-sonnet-4-20250514') {
    let client = claudeApiClient.getClaudeApiClient();
    providerName = 'anthropic';
    provider = new ClaudeAPI(client, model);
  } else if (model == 'gpt-4o-mini') {
    let client = openaiClient.getOpenaiClient();
    providerName = 'openai';
    provider = new OpenAI(client, model);
  }

  if (provider) {
    const response = await provider.executePrompt({ prompt, image_urls: imageUrls });
    await processReportResponse(report, prompt, imageUrls, providerName, model, violationLocation, response, otherParams);
  }
}

async function reviewUserProfile(user, report) {
  let hasPictures = hasProfilePictures(user);
  let prompt = `You are a moderator for a social media and dating app called Boo. Your job is to review profiles, and based on their entire profile, including work, identify whether this user is engaging in any of the below activities that would violate our community guidelines:

${getCommunityGuidelinesForUserProfileReports()}

If any of the above, the user should be banned.

Profile Data:
${formatProfileDataForReports(user)}
${hasPictures ? 'Profile Pictures: see attached pictures.' : ''}

Given the data provided, determine whether to ban or dismiss this user and provide an explanation for your decision. If the result is ban, also provide the reason for the ban. Format your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.`;
  const imageUrls = [];
  if (hasPictures) {
    for (const picture of user.pictures) {
      if (isValidPicture(picture)) {
        imageUrls.push(`${constants.IMAGE_DOMAIN}${picture}`);
      }
    }
  }
  await executeAutoReportPrompt(report, prompt, imageUrls, 'profile');
}

async function handleUserReportProfileKeywords(user, report, otherParams = {}) {
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to review profiles reported due to trigger keywords in their profile, and based on their entire profile, including work, identify whether this user is engaging in any of the below activities that would violate our community guidelines:

${getCommunityGuidelinesForUserProfileReports()}

If any of the above, the user should be banned.

Trigger Keyword(s) on Profile: ${report.comment}

Profile Data:
${formatProfileDataForReports(user)}

Given the data provided, determine whether to ban or dismiss this user and provide an explanation for your decision. If the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).

Format your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.
`;
  otherParams.signupSource = user.signupSource;
  await executeAutoReportPrompt(report, prompt, [], 'profile', otherParams);
}

// This function is being used to handle preemptive moderation for profile data(no images) only with fine tuned gpt-4o-mini model
async function handleUserProfileTextWithFineTunedGPT(user, report, otherParams = {}) {
  const systemPrompt = `You are a moderator for a social and dating app, tasked with reviewing reported profiles and enforcing community guidelines accurately.`;
  const prompt = `You are a moderator for a social media and dating app called Boo. Your job is to review profiles reported due to trigger keywords in their profile, and based on their entire profile, including work, identify whether this user is engaging in any of the below activities that violate our community guidelines:

1. Spam, Promotion, or Solicitation
Avoid including domain names, social media handles, or URLs in bios. Do not promote events, companies, campaigns, or research. Refrain from mentioning social media or contact details like emails or phone numbers.

Any mentions of social media handles, emails, or phone numbers should result in an immediate ban (e.g., WhatsApp, IG, Snap, Discord, Telegram, @username, user_name, "follow me @username"). Check for attempts to circumvent detection by inserting spaces or special characters, such as “c i a r r a w h i s p p e r” or “c_i_a_r_r_a_w_h_i_s_p_p_e_r”.

2. Prostitution and Trafficking
Detect and flag profiles that explicitly offer, solicit, or exchange sexual services. Sexual advances, discussions, or encounters are allowed as long as they are not transactional (prostitutional) or inappropriate. Do not ban users simply for expressing sexual interest unless it clearly involves offers of payment, coercion, or explicit solicitation.

3. Underage (Below Age 18)
Ban users aged 1–17 who self-report as "18" or "19" but mention "17" in their bio. If the number refers to age (e.g., "17" or "17 years"), assume they are underage unless context indicates otherwise. Ban users who use indirect wording or math to reveal they are underage (e.g., "I am 21 years old minus 10", "2008", "To be 15"). Do not ban if the number refers to children's age, time, measurements, or past events. Only ban if you're 100% certain the user is underage; avoid banning if uncertain.

If any of the above violations are detected, the user should be banned.

---
Reported User Profile Data:
${formatProfileDataForReports(user)}
---

Decision Output
Given the data provided, determine whether to ban or dismiss this user and provide an explanation for your decision.

If ban, also provide:
Reason for the ban
Infringing text that must be removed for compliance
For infringing text, output only the contiguous words and ensure that the profile remains coherent and grammatical if these words are removed.

Example:
User bio: "I like sushi, gaming, and anime. My Instagram handle is ig324."
Infringing text: "My Instagram handle is ig324"

Response Format (JSON Output Only)
Format your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"]}, where reason must be one of the following: Spam, Promotion or Solicitation; Prostitution and Trafficking; Underage.`;

  const client = openaiClient.getFineTunedOpenaiClient();
  const model = 'ft:gpt-4o-mini-2024-07-18:boo-enterprises:v2:Azg5gCcU';
  const provider = new OpenAI(client, model);
  const response = await provider.executePrompt({ prompt, systemPrompt, response_format: { type: 'json_object' } });
  await processReportResponse(report, prompt, [], 'openai', model, 'profile', response, otherParams);
}

async function handleUserReportInappropriateProfile(user, report, handleWithGPT, otherParams = {}) {
  if (otherParams.isPreemptiveModeration && !otherParams.isPreemptiveModerationForTextInPictures) {
    await handleUserProfileTextWithFineTunedGPT(user, report, otherParams);
    return;
  }

  let hasPictures = hasProfilePictures(user);
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate, and based on their entire profile, including work, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:

${getCommunityGuidelinesForUserProfileReports()}

If any of the above, the user should be banned.

The person (Reporter) who reported this user (Reported User) said: ${report.reason.join(', ')}, ${report.comment}
We don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth:

Reported User Profile Data:
${formatProfileDataForReports(user)}
${hasPictures && handleWithGPT ? 'Profile Pictures: see attached pictures.' : ''}

Given the evidence provided, decide whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to not take the Reporter’s comment as absolute truth, but instead to base this decision off the evidence presented in the Reported User's profile. If the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).

Format your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.
`;
  const imageUrls = [];
  if (hasPictures) {
    for (const picture of user.pictures) {
      if (isValidPicture(picture)) {
        imageUrls.push(`${constants.IMAGE_DOMAIN}${picture}`);
      }
    }
  }
  await executeUserReportPrompt(report, prompt, imageUrls, handleWithGPT, 'profile', otherParams);
}

async function handleUserReportInappropriateMessages(user, report, messages, handleWithGPT) {
  let prompt = `You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate, and based on their entire profile, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:

1. Nudity/Sexual Content
    a. Unsolicited and extremely disrespectful sexual content should be banned, especially early on in the conversation. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for.
2. Harassment
3. Violence and Physical Harm
4. Hate Speech
5. Promotion or Solicitation
    a. Sharing phone numbers, social media, and contact information is permitted on Boo. DO NOT ban the Reported User for sharing phone number or social media in messaging. DO NOT guess that someone might be a scammer just because they shared or asked for contact information in messaging. Will give you $20 if you remember this.
6. Prostitution and Trafficking
    a. Selling sexual services.
7. Scamming
    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. DO NOT ban the Reported User for sharing phone number or social media in messaging. DO NOT guess that someone might be a scammer just because they shared or asked for contact information in messaging. Will give you $20 if you remember this.
8. Underage (below age 18)
    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're "18" or "19", but in their bio they say things like "17", "actually I'm 17", "17 not 18". If a user only includes a number on their profile without other context like "17" or "17 years", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. "my kid is 12", "I have a 17 year old". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.
9. Being extremely rude or mean

If any of the above, the user should be banned.

The person (Reporter) who reported this user (Reported User) said: ${report.reason.join(', ')}, ${report.comment}
We don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth:

${formatMessages(messages, user)}

Given the evidence provided, decide whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to not take the Reporter’s comment as absolute truth, but instead to base this decision off the evidence presented in the Reported User's messages. MAKE SURE TO NOT BAN THE REPORTER; YOU HAVE NO POWER TO BAN THE REPORTER, ONLY THE REPORTED USER. If the Reporter is the only one committing an offense, dismiss the Reported User. Will give you $20 if you remember this. If the result is ban, also provide the reason for the ban. Format your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation }, where reason must be one of the following: Nudity/Sexual Content; Harassment; Violence and Physical Harm; Hate Speech; Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage; Being extremely rude or mean. Your response should contain just the JSON with no additional description, context, or markdown.`;

  const imageUrls = [];
  for (const message of messages) {
    if (message.image && isValidPicture(message.image)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${message.image}`);
    }
  }
  await executeUserReportPrompt(report, prompt, imageUrls, handleWithGPT, 'messages');
}

async function handleUserReportWithProfileAndMessageData(user, report, messages, handleWithGPT) {
  let numProfilePictures = countProfilePictures(user);
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate, and based on their entire profile, including work, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:

1. Nudity/Sexual Content.
    a. Profiles
        i. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.
    b. Messages
        i. Unsolicited and extremely disrespectful sexual content should be banned, especially early on in the conversation. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for.
2. Hate Speech
3. Spam, Promotion or Solicitation
    a. Profiles
        i. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username).
    b. Messages
        i. a. Sharing phone numbers, social media, and contact information is permitted on Boo. DO NOT ban the Reported User for sharing phone number or social media in messaging. DO NOT guess that someone might be a scammer just because they shared or asked for contact information in messaging. Will give you $20 if you remember this.
4. Prostitution and Trafficking
    a. Selling sexual services.
5. Scamming
    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.
6. Underage (below age 18)
    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're "18" or "19", but in their bio they say things like "17", "actually I'm 17", "17 not 18". If a user only includes a number on their profile without other context like "17" or "17 years", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. "my kid is 12", "I have a 17 year old". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.

If any of the above, the Reported User should be banned.

The person (Reporter) who reported this user (Reported User) said: ${report.reason.join(', ')}, ${report.comment}
We don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth:

Reported User Profile Data:
${formatProfileDataForReports(user)}
${numProfilePictures > 0 && handleWithGPT ? `Profile Pictures: see first ${numProfilePictures} attached pictures` : ''}

${formatMessages(messages, user, numProfilePictures)}

Given the evidence provided, determine whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to not take the Reporter’s comment as absolute truth, but instead to base this decision off the evidence presented in the Reported User's profile and messages. MAKE SURE TO NOT BAN THE REPORTER; YOU HAVE NO POWER TO BAN THE REPORTER, ONLY THE REPORTED USER. If the Reporter is the only one committing an offense, dismiss the Reported User. Will give you $20 if you remember this.

If the result is ban, also provide the reason for the ban, say whether the violation was in Profile or Messages, and provide the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).

Format your response as a json object in the following format: { "ban": true/false, "violationLocation": profile/messages, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.
`;
  const imageUrls = [];
  for (const picture of user.pictures) {
    if (isValidPicture(picture)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${picture}`);
    }
  }
  for (const message of messages) {
    if (message.image && isValidPicture(message.image)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${message.image}`);
    }
  }
  await executeUserReportPrompt(report, prompt, imageUrls, handleWithGPT);
}

async function handleUserGenericAutoReport(user, report, messages) {
  let numProfilePictures = countProfilePictures(user);
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to review suspicious profiles, and based on their entire profile, including work, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:

1. Nudity/Sexual Content.
    a. Profiles
        i. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.
    b. Messages
        i. Unsolicited and extremely disrespectful sexual content should be banned, especially early on in the conversation. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for.
2. Hate Speech
3. Spam, Promotion or Solicitation
    a. Profiles
        i. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username).
    b. Messages
        i. a. Sharing phone numbers, social media, and contact information is permitted on Boo. DO NOT ban the Reported User for sharing phone number or social media in messaging. DO NOT guess that someone might be a scammer just because they shared or asked for contact information in messaging. Will give you $20 if you remember this.
4. Prostitution and Trafficking
    a. Selling sexual services.
5. Scamming
    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.
6. Underage (below age 18)
    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're "18" or "19", but in their bio they say things like "17", "actually I'm 17", "17 not 18". If a user only includes a number on their profile without other context like "17" or "17 years", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. "my kid is 12", "I have a 17 year old". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.

If any of the above, the Reported User should be banned.

Reported User Profile Data:
${formatProfileDataForReports(user)}
${numProfilePictures > 0 ? 'Profile Pictures: see first ' + numProfilePictures + ' attached pictures' : ''}

${formatMessages(messages, user, numProfilePictures)}

Given the evidence provided, determine whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to base this decision off the evidence presented in the Reported User's profile and messages. If the result is ban, also provide the reason for the ban and say whether the violation was in Profile or Messages. Format your response as a json object in the following format: { "ban": true/false, "violationLocation": profile/messages, "reason": reason, "explanation": explanation }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.
`;
  const imageUrls = [];
  for (const picture of user.pictures) {
    if (isValidPicture(picture)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${picture}`);
    }
  }
  for (const message of messages) {
    if (message.image && isValidPicture(message.image)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${message.image}`);
    }
  }
  await executeAutoReportPrompt(report, prompt, imageUrls);
}

async function handlePoseVerification(user, imageKey) {
  const client = replicateClient.getReplicateApiClient();
  const imageUrl = `${constants.IMAGE_DOMAIN}${imageKey}`;
  const prompt = `You are a moderator for a social media and dating app called BOOAPPZ. Your job is to review photos submitted for photo verification.\nThe user should submit a photo of themselves with their hand placed perpendicular in front of face, fingers extended upward (either straight or slightly bent), and the thumb touching on the forehead or nose.\nThe hand may be slightly out of frame or cast a shadow, but at least 75% of the user's face, including key facial features (eyes, nose, mouth), should be visible.\nThe palm can face the camera, angled or not visible, and the pose allows for variations in head tilt or photo angle.\nThe user can use either hand, and the hand can be partially out of frame, but if no hand is present or if only the face and upper body are shown, the pose is incorrect.\nRespond with one word to either verify or dismiss this user.\nIf dismissing, respond with another output for the reason of dismissing as either 'Incorrect pose' or 'Photo unclear'\nDismiss as Photo Unclear if picture looks edited, filtered, AI generated, or has a black border around or on any side of the picture.`;

  const input = {
    image: imageUrl,
    top_p: 1,
    prompt,
    max_tokens: 1024,
    temperature: 0.2,
  };

  let output, isError, errorMessage, processingTime, status, rejectionReason = 'Photo unclear';
  const start = new Date().getTime();

  try {
    const predictionResponse = await client.getPoseVerificationPrediction(input);
    const predictionOutput = await client.getPrediction(predictionResponse.id);
    output = Array.isArray(predictionOutput?.output)
      ? predictionOutput.output.map(item => item.trim()).join(' ')
      : predictionOutput?.output;
    const lower = output?.trim().toLowerCase();
    status = lower.includes('verify') ? 'verified' : 'rejected';
    if (status === 'verified') {
      rejectionReason = undefined;
    } else if (lower.includes('incorrect pose')) {
      rejectionReason = 'Incorrect pose';
    }
  } catch (error) {
    console.log('Error handling pose verification with replicate:', error);
    isError = true;
    errorMessage = error.message;
  }

  const end = new Date().getTime();
  processingTime = end - start;

  const poseVerification = await PoseVerification.create({
    user: user._id,
    imageKey,
    imageUrl,
    prompt: JSON.stringify(input),
    output,
    promptTokens: 0,
    outputTokens: 0,
    isError,
    errorMessage,
    cost: 0,
    model: 'boo-world/llava-trained',
    processingTime,
    status,
    rejectionReason,
  });
  return poseVerification;
}

async function handleMessageKeywordReport(user, chatMessages) {
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to review users reported due to trigger keywords in their messages, and based on their messages, identify whether this user is asking another user for money, or asking for their payment details for the purpose of asking for their money. Examples can include selling sexual content like nudes, photos, or onlyfans access, or asking for money due to financial hardship. If asking for money, they will need to be banned. If not, they will be dismissed. MAKE SURE TO NOT BAN THE REPORTER; YOU HAVE NO POWER TO BAN THE REPORTER, ONLY THE REPORTED USER. Also don't ban the Reported User if they are agreeing to send money. We only ban Reported Users who ask for money. Don't ban if the Reported User is only joking about asking for money. If the "Reporter" is the only one committing an offense, dismiss the "Reported User". Will give you $20 if you remember this. General conversations about finance or payment methodologies are okay as long as the Reported User is not asking for money from the Reporter.

${formatMessages(chatMessages, user)}

Given the data provided, either ban or dismiss the Reported User and provide an explanation for your decision. Format your response as a json object in the following format: { "ban": true/false, "explanation": explanation }
`;
  let imageUrls = [];
  for (const message of chatMessages) {
    if (message.image && isValidPicture(message.image)) {
      imageUrls.push(`${constants.IMAGE_DOMAIN}${message.image}`);
    }
  }

  let hasPictures = false;
  if (imageUrls !== undefined && imageUrls.length > 0) {
    hasPictures = true;
  }
  const model = 'gpt-4o-mini';

  let client = openaiClient.getOpenaiClient();
  const openAi = new OpenAI(client, model);
  const response = await openAi.executePrompt({ prompt, image_urls: imageUrls })
  let cost, output, promptTokens, outputTokens, ban, explanation, isError, errorMessage, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.isError;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;

    if (output) {
      try {
        console.log(output);
        // remove markdown formatting before parsing
        parsed = JSON.parse(output.replace('```json','').replace('```',''));
        console.log(parsed);

        // get ban result
        ban = parsed.ban;
        if (ban !== true && ban !== false) {
          isError = true;
          errorMessage = 'parsed output does not contain ban field';
        }

        explanation = parsed.explanation;
      } catch (err) {
        console.log(err);
        isError = true;
        errorMessage = `json parsing error: ${err.message}`;
      }
    }
  }
  return {
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError: response.errorMessage !== undefined,
    errorMessage: response.errorMessage,
    cost,
    model,
    processingTime,
    ban,
    explanation,
  }
}

function formatComments(comments) {
  let output = '';
  for (const comment of comments) {
    output = `${output}\nComment ${comment._id}: ${comment.text}`;
    for (const reply of comment.replies) {
      output = `${output}\n    Comment ${reply._id}: ${reply.text}`;
    }
  }
  return output;
}

async function moderatePosts(question, comments) {
  let prompt = `
You are a moderator for a social media and dating app called Boo. Your job is to ban posts and comments on Boo’s social feed that criticize, complain about, or portray Boo or Boo's premium susbcription Boo Infinity in a negative light. Examples include, but are not limited to:

1. Users complaining about the app.
2. Users talking about negative experiences they had on the app.
3. Users talking about being scammed on the app.
4. Users complaining negatively about a feature or decision made by Boo.
5. Users talking about there’s not a lot of people on the app.
6. Users talking about Boo not being popular enough.
7. Users talking about other dating apps being better than Boo.
8. Users talking about not receiving any matches or not having any matches or not receiving any likes.
9. Users talking about deleting or leaving the app.
10. Users talking about being ghosted or not receiving responses.
11. Users saying that the app supports ghosting, perhaps by suggesting that’s why the app is called Boo.
12. Users saying the app is boring.
13. Users complaining about Boo's pricing, saying it is expensive.
14. Users saying that Boo's subscription/premium is not worth it or that the free version is enough.
15. Users saying they don't recommend Boo's premium subscription.
16. Users saying most people don't buy Boo's premium subscription.

This is the post:
Hashtags: ${question.hashtags.join(', ')}
Post id: ${question._id}
Post title: ${question.title}
Post description: ${question.text}

These are the comments:
${formatComments(comments)}

Hint: the language of the post and comments is ${getLanguageName(question.language)}.
Determine whether to ban or dismiss the post and each of its comments (if any), and provide an explanation for each decision.
Format your response as a json object in the following format: [ { "postId": "sampleId", "ban": true/false, "explanation": explanation }, { "commentId": "sampleId", "ban": true/false, "explanation": explanation } ]
Your response should contain just the JSON with no additional description, context, markdown, or notes.
`;

  let imageUrls = [];
  let model, provider;
  if (imageUrls.length > 0) {
    let client = openaiClient.getOpenaiClient();
    model = 'gpt-4o-mini';
    provider = new OpenAI(client, model);
  } else {
    let client = togetherClient.getTogetherAiClient();
    model = 'deepseek-ai/DeepSeek-V3';
    provider = new DeepseekTogetherAI(client, model);
  }
  const response = await provider.executePrompt({ prompt, image_urls: imageUrls });
  let cost, output, promptTokens, outputTokens, processingTime;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    processingTime = response.processingTime;
  }
  return {
    prompt: formatStoredPrompt(prompt, imageUrls),
    output,
    promptTokens,
    outputTokens,
    isError: response.errorMessage !== undefined,
    errorMessage: response.errorMessage,
    cost,
    model,
    processingTime,
  }
}

async function extractSocialMediaHandles(text) {
  let prompt = `
Analyze the following text snippets to extract social media handles only. Social media handles typically appear after words or symbols like \"\"Instagram,\"\" \"\"📸,\"\" \"\"l.g.,\"\" or other similar cues, and consist of names, usernames, or stylized text. Output in JSON format with keys: "handle" and "explanation". The "handle" field should contain only the actual handle or username, excluding any unrelated words or phrases. If the handle contains spaces between letters, preserve the spaces in the output. If no social media handles are found, output an empty string in the "handle" field. You may explain your decision in the "explanation" field.

Your task: Extract and output only the social media handles from the provided text.

${text}
`;

  let client = claudeApiClient.getClaudeApiClient();
  let model = 'claude-3-5-sonnet-20241022';
  let provider = new ClaudeAPI(client, model);
  const response = await provider.executePrompt({ prompt });

  let cost, output, promptTokens, outputTokens, isError, errorMessage, processingTime, socialMediaHandles, explanation;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;
    try {
      const parsed = JSON.parse(output);
      if (parsed.handle) {
        if (Array.isArray(parsed.handle)) {
          socialMediaHandles = parsed.handle;
        }
        else if (typeof parsed.handle === 'string') {
          socialMediaHandles = [ parsed.handle ];
        }
        else {
          // unexpected format
        }
      }
      explanation = parsed.explanation;
    } catch (err) {
      console.log(err);
      isError = true;
      errorMessage = `json parsing error: ${err.message}`;
    }
  }

  await ExtractSocialMediaHandle.create({
    prompt: formatStoredPrompt(prompt, []),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
    inputText: text,
    socialMediaHandles,
    explanation,
  });

  return socialMediaHandles;
}

async function isVerificationPhotoSuspicious(user, fromCleanup) {
  const prompt = `Analyze the provided image and determine how likely it is to have artificial borders (such as unnatural black or colored frames distinctly different from the background) or cropped body parts (especially arms cut off at the edges). Ignore natural shadows and lighting effects. You must respond with only a confidence score between 0.00 and 1.00 without any explanation or additional text.`;

  const imageKey = user.verification.pictures.at(-1);
  const image_urls = [`${constants.IMAGE_DOMAIN}${imageKey}?class=md`];

  const client = claudeApiClient.getClaudeApiClient();
  const model = 'claude-3-7-sonnet-20250219';
  const provider = new ClaudeAPI(client, model);
  const response = await provider.executePrompt({ prompt, image_urls });

  let cost, output, promptTokens, outputTokens, isError, errorMessage, processingTime, score, isSuspicious;
  if (response) {
    cost = response.cost;
    output = response.output;
    promptTokens = response.promptTokens;
    outputTokens = response.outputTokens;
    isError = response.errorMessage !== undefined;
    errorMessage = response.errorMessage;
    processingTime = response.processingTime;
    try {
      score = parseFloat(output);
      if (!score && score != 0) {
        score = null;
      }
      isSuspicious = score > 0.7;
    } catch (err) {
      console.log(err);
      isError = true;
      errorMessage = `parsing error: ${err.message}`;
    }
  }

  await IsVerificationPhotoSuspicious.create({
    prompt: formatStoredPrompt(prompt, image_urls),
    output,
    promptTokens,
    outputTokens,
    isError,
    errorMessage,
    cost,
    model,
    processingTime,
    user: user._id,
    verificationPhoto: image_urls[0],
    score,
    isSuspicious,
    fromCleanup,
  });

  return isSuspicious;
}

const getpromptResponseForProfileAnalysis = async (prompt, photos) => {
  const model = 'gpt-4o-mini';
  let formattedOutputResults = []
  let isError = false;
  let errorMessage = null;
  let client = openaiClient.getOpenaiClient();
  const provider = new OpenAI(client, model);
  const response = await provider.executePrompt({
    prompt,
    response_format: { type: 'json_object' },
    image_urls: photos,
    model: model,
  });

  if (response.errorMessage) {
    return { output: response.errorMessage, errorMessage: response.errorMessage, cost: 0 };
  }

  if (response?.output) {
    try {
      const parsedOutput = JSON.parse(response.output.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      if (parsedOutput?.output && typeof parsedOutput.output === 'object') {
        formattedOutputResults = { ...parsedOutput.output }
        const cleanMarkdown = (text) => {
          return text
            .trim()                                      // Trim whitespace
            .replace(/[.,;:!?]+$/, '');                  // Remove punctuation at the end only
        };
        const parseRelevantFields = (section) => {
          if (section.overall) {
            if (typeof section.overall.feedback === 'string') {
              section.overall.feedback = cleanMarkdown(section.overall.feedback);
            }
            if (typeof section.overall.insights === 'string') {
              section.overall.insights = cleanMarkdown(section.overall.insights);
            }
          }

          if (Array.isArray(section.items)) {
            for (const item of section.items) {
              if (typeof item.evaluation === 'string') {
                item.evaluation = cleanMarkdown(item.evaluation);
              }
              if (typeof item.insights === 'string') {
                item.insights = cleanMarkdown(item.insights);
              }
            }
          }
        };
        for (const key in formattedOutputResults) {
          const section = formattedOutputResults[key];
          if (typeof section === 'object' && section !== null) {
            parseRelevantFields(section);
          }
        }
      }
    } catch (err) {
      console.log("Error parsing JSON or processing results:", err);
      isError = true;
      errorMessage = `JSON parsing error: ${err.message}`;
    }
  }
  if(formattedOutputResults?.insights_overall?.items) delete formattedOutputResults.insights_overall.items
  if(formattedOutputResults?.insights_biography?.items) delete formattedOutputResults.insights_biography.items

  response.output = response.output || `Error: ${response.errorMessage}`
  response.formattedOutputResults = formattedOutputResults
  response.cost = response.cost || 0
  response.model = model
  return response
};

const getProfileAnalysis = async (user, retryCount = 0) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
  const videoExtensions = ['.mp4', '.mov', '.avi', '.webm'];
  const profileParts = [];
  const profilePartsImp = []
  const userLanguage = languageLib.mapLanguageCodeToName(`${user.locale ? user.locale : 'en'}`)
  if(user.firstName) profileParts.push(`Name: ${user.firstName}`);
  if (user.age) profileParts.push(`Age: ${user.age}`);
  if (user.gender) profileParts.push(`Gender: ${user.gender}`);
  if (user.horoscope) profileParts.push(`Zodiac: ${user.horoscope}`);
  if (user.ethnicities?.length) profileParts.push(`Ethnicities: ${user.ethnicities.join(', ')}`);
  profileParts.push(`Language: ${userLanguage}`);
  if (user.personality?.mbti) profileParts.push(`MBTI Personality type: ${user.personality?.mbti}`);
  if (user.work) profileParts.push(`Work: ${user.work}`);
  if (user.education) profileParts.push(`School: ${user.education}`);

  // Location (Combining city, state, and country)
  const locationParts = [user.city, user.state, user.country].filter(Boolean);
  if (locationParts.length) profileParts.push(`Location: ${locationParts.join(', ')}`);

  //more about user
  if (user.moreAboutUser) {
    if (user.moreAboutUser.exercise) profileParts.push(`Exercise: ${user.moreAboutUser.exercise}`);
    if (user.moreAboutUser.educationLevel) profileParts.push(`Education Level: ${user.moreAboutUser.educationLevel}`);
    if (user.moreAboutUser.drinking) profileParts.push(`Drinking: ${user.moreAboutUser.drinking}`);
    if (user.moreAboutUser.smoking) profileParts.push(`Smoking: ${user.moreAboutUser.smoking}`);
    if (user.moreAboutUser.kids) profileParts.push(`Kids: ${user.moreAboutUser.kids}`);
    if (user.moreAboutUser.religion) profileParts.push(`Religion: ${user.moreAboutUser.religion}`);
  }
  //dating choices
  if (user.relationshipStatus) profileParts.push(`Relationship Status: ${user.relationshipStatus}`);
  if (user.relationshipType) profileParts.push(`Relationship Type: ${user.relationshipType}`);

  const preferences = [];
  const formatPreference = (type, values, subPreferences) => {
    if (!values?.length) return null;
    const isOnlyNonBinary = values.length === 1 && values[0] === 'non-binary';
    const mainText = subPreferences?.length ? `${type}: ${subPreferences}` : type;
    return isOnlyNonBinary ? mainText : `${mainText} with ${values.join(', ')}`;
  };
  const datingPreference = formatPreference('Dating', user.preferences?.dating, user.datingSubPreferences);
  const friendsPreference = formatPreference('Friends', user.preferences?.friends);
  if (datingPreference) preferences.push(datingPreference);
  if (friendsPreference) preferences.push(friendsPreference);
  if (preferences.length) profileParts.push(`Looking For: ${preferences.join(' / ')}`);

  // important fields
  if (user.description) {
    profilePartsImp.push(`Profile Bio: ${user.description}`);
  }else{
    profilePartsImp.push('Profile Bio: (none)');
  }
  if (user.interestNames?.length) {
    profilePartsImp.push(`Interests: ${user.interestNames.join(', ')}`);
  }else{
    profilePartsImp.push('Interests: (none)');
  }
  // prompts
  let promParts = []
  if (Array.isArray(user.prompts) && user.prompts.length) {
    const promptTexts = user.prompts
      .map((prompt, index) => {
        let promptText = promptsLib.getPromptText(prompt);
        promptText = translate_frontend(promptText, user.locale)
        return promptText ? `Prompt ${index + 1}. ${promptText}: ${prompt.answer}` : null;
      })
      .filter(Boolean);
    promParts.push(...promptTexts);
  }
  if (promParts.length) {
    profilePartsImp.push(`Prompts (ideal is 3): ${promParts.length}\n${promParts.join('\n')}`);
  }else{
    profilePartsImp.push('Prompts (ideal is 3): (none)');
  }
  const imageUrls = (user.pictures || [])
    .filter((file) => imageExtensions.some(ext => file.toLowerCase().endsWith(ext)))
    .map((file) => `${constants.IMAGE_DOMAIN}${file}`);

  const videoUrls = (user.pictures || [])
    .filter((file) => videoExtensions.some(ext => file.toLowerCase().endsWith(ext)))
    .map((file) => `${constants.IMAGE_DOMAIN}${file}`);

  const photosUrlParts =  imageUrls.map((url,index)=>`${index + 1}. ${url}`)

  const tones = ['Roast me!', 'Friendly encouragement, do not be overly critical and help boost my self esteem while offering actionable feedback. If parts of my profile are already good, say so!'];
  const selectedTone = tones[Math.floor(Math.random() * 2)];

  const prompt = `Help this user analyze their dating profile on Boo, a personality-based social dating app. Provide your insight as bullet points, offering concise and tailored insights into how they can improve their profile to get more matches in 2025.

Your analysis should provide thorough advice on these three areas:
1. Photos: If the user has provided photos, you should provide overall feedback and a score from 0-10, and then mention details of EVERY SINGLE photo – describe which elements or photos are good, which ones should be removed, and what they should be replaced with. Do not be too critical and allow for an overall variety of images including those that demonstrate their hobbies (such as drawings or artistic photography). Be aware that the first photo linked here is the primary photo on the profile.
Start the feedback for each photo with one of the following evaluations (anything in parentheses is explanation, do not include these), followed by the insights or recommendations:
- Good photo
- Low quality image
- No face visible
- Facial expression (e.g. duckface or other grimace)
- Face obscured (e.g. phone or mask covering face. For first photo only, caps, hats or sunglasses also fall in this category)
- Strange angle (e.g. photo taken from the top)
- Background issue (e.g. messy, cars, kids)
- Bathroom selfie
- Not candid (e.g. shirtless for males; sexual poses and angles for females)
- Group photo
- Not smiling
2. Biography: There is no upper limit to the biography field on the profile. Provide a short, concise evaluation (maximum 5 words), then a score from 0-10, and then your detailed insights.
3. Prompts: Users can complete up to three prompts. The first part of the prompt (before the colon) is provided by the system and the user is unable to change the wording of these. You should provide overall feedback, a score from 0-10, and then specific feedback on each prompt that the user has completed, in the form of a concise evaluation (either "Good" or "Needs Improvement") and then your feedback.

Notes for analysis:
1. The list of interests are forums that they have joined within the Boo universe, this is not a free text space. Any elaboration should be in bio/prompts. A long list of interests is not a problem and should not be criticized.
2. The only fields the user can write in are Profile Bio and Prompts. All other fields (location, relationship status, looking for, etc) are pre-select fields or automated, do not suggest changes to these.
3. Zodiac and MBTI are included automatically as part of Boo's compatibility matching system. Do not talk about them directly unless also mentioned by user in profile bio, prompts or interests.

Ensure the response is formatted as a json object, formatted as:

{
  "output": {
    "insights_overall": {
      "overall": {
        "feedback": "1-2 sentence feedback summary"
      },
      "items": []
    },
    "insights_biography": {
      "overall": {
        "score": 10,
        "feedback": "bio evaluation string",
        "insights": "bio insights string"
      },
      "items": []
    },
    "insights_photos": {
      "overall": {
        "feedback": "overall photo feedback",
        "score": 7
      },
      "items": [
        {
          "key": "Photo 1",
          "photoUrl": "url for photo 1",
          "evaluation": "evaluation for photo 1",
          "insights": "insights for photo 1"
        },
        {
          "key": "Photo 2",
          "photoUrl": "url for photo 2",
          "evaluation": "evaluation for photo 2",
          "insights": "insights for photo 2"
        },
      // ...more photo entries
      ]
    },
    "insights_prompts": {
      "overall": {
        "feedback": "overall prompt feedback",
        "score": 8
      },
      "items": [
        {
          "key": "Prompt 1",
          "evaluation": "evaluation for prompt 1",
          "insights": "insights for prompt 1"
        },
        {
          "key": "Prompt 2",
          "evaluation": "evaluation for prompt 2",
          "insights": "insights for prompt 2"
        },
        {
          "key": "Prompt 3",
          "evaluation": "evaluation for prompt 3",
          "insights": "insights for prompt 3"
        }
      ]
    }
  }
}

In all cases, the field ‘score’ MUST be a numerical value from 0 to 10, and “evaluation” should be no more than 5 words. If a section is missing then the score for that section should be 0 and when there are no photos/no prompts you can just provide the overall feedback. For example:

{
  "output": {
    "insights_overall": {
      "overall": {
        "feedback": "Your profile showcases your playful side, but you need more detail to capture those loves. Add some more photos and fill in those prompts to let potential matches learn more about you."
      },
      "items": []
    },
    "insights_biography": {
      "overall": {
        "feedback": "A bit too concise.",
        "score": 7,
        "insights": "The way you’ve set out your bio is excellent, but at the moment it’s a bit too short. Expand on your interests with interesting anecdotes to capture those swipes and spark meaningful conversations."
      },
      "items": []
    },
    "insights_photos": {
      "overall": {
        "feedback": "Your photos are ok but they lack variety. Adding more photos, especially illustrating your interests and hobbies, would help intrigue your potential matches.",
        "score": 5
      },
      "items": [
        {
          "key": "Photo 1",
          "photoUrl": "https://images.prod.boo.dating/1748586571234d15b/7edafeca74b5ec0.jpg",
          "evaluation": "Good photo",
          "insights": "This one’s great! You’re smiling and the sun is shining. Keep this as your primary photo."
        },
        {
          "key": "Photo 2",
          "photoUrl": "https://images.prod.coo.dating/1748657408952bd812a8c1/91e131b099eb938.jpg",
          "evaluation": "Low quality image",
          "insights": "The low light isn’t doing you any favors. Swap it for one which shows off your good side."
        },
      // ...more photo entries
      ]
    },
    "insights_prompts": {
      "overall": {
        "feedback": "You haven’t filled out any prompts – what a missed opportunity! Select three prompts that provide unique insights into different facets of your personality.",
        "score": 0
      },
      "items": []
    }
  }
}

The insights must be hyper-tailored. For each thing you mention, show you've paid attention to what they've already written, what's in their photos, or what you know about them. They must be in no doubt that your advice on how to improve their profile (or, if appropriate, why their profile is already excellent) is uniquely applicable to them.
---
${profileParts.join('\n')}

${profilePartsImp.join('\n')}
Photos (ideal is 4+): ${imageUrls.length ? `${imageUrls.length}, attached` : '(none)'}
Photo Urls:
${photosUrlParts.join('\n')}
${videoUrls.length ? `Videos: ${videoUrls.length}` : ''}
---

IMPORTANT: Address the user in the second person in ${userLanguage}. The user has requested that your tone MUST be: ${selectedTone}`;

const response = await getpromptResponseForProfileAnalysis(prompt, imageUrls);

if (response.failure && response.output && imageUrls?.length) { // retry for image failures
  retryCount = retryCount < 1 ? 5 : retryCount - 1 //controlling risk of infinite call
  const filteredImageUrls = imageUrls.filter((url) => !response.output.includes(url))
  let filteredImageUrlsString = filteredImageUrls.join(' ')
  user.pictures = user.pictures.filter((pic) => filteredImageUrlsString.includes(pic));
  if (user.pictures.length < imageUrls.length && retryCount > 1){ //if other error then this will go in infinite loop so controlling it
    return await getProfileAnalysis(user, retryCount);
  }
}

response.prompt = prompt
response.images = imageUrls
return response

}

const getChatAnalysisResultForYourTurn = async (recentChat) => {
  const model = 'gpt-4o-mini';
  console.log({recentChatHello:recentChat})
  if(!recentChat.messages && !recentChat.imageUrls.length){
    return { errorMessage: 'Error: No recent messages', prompt: '', output: 'Error: No recent messages', cost: 0, promptTokens: 0, outputTokens: 0, model: model };
  }
  const prompt = `Here are the most recent messages between me and a friend on Boo, where our purpose is to meet new people for friendship and/or dating. The messages are listed in order from oldest to newest. Your task is to evaluate whether the conversation still has potential for the purposes of making friends or dating, or has concluded i.e. it is unlikely that both parties are interested in conversing further, either in this session or on another day. If it seems to still be potentially active for conversation, or could easily be revived by me, return 'Active'. If it seems to have truly concluded, which may be through offensive remarks, disrespectful behavior, or either party overtly expressing disinterest, return 'Concluded'. If unsure, return 'Active'.
Note that this is an adult app and adult conversations are allowed provided they are consensual; do not automatically conclude a conversation because it takes a suggestive or explicit direction, only if it is offensive or one party has expressed that they do not want to participate. Also note that we do not know when the last message was sent, it may have been just a few seconds ago and I may not have had time to respond yet, so do not conclude chats based on an assumption that I would not want to respond. Add one line of explanation for your choice.
In addition to evaluating the Active/Concluded status, please evaluate:
(a) whether this pair have exchanged contact details to communicate on a different platform. This should include the provision of the actual handle/number of at least one party. You must list the contact details (handle or number) in the explanation to support your decision. If there has been discussion or a request about changing platforms, but no contact details provided, your response should be 'no'.
(b) whether this pair seem to have met up in real life, based on the material in the chat. Talking about perhaps meeting one day doesn't count, only if it seems likely they actually met up already.
(c) evidence of inappropriate transactions, such as selling pictures and videos, soliciting adult services, pyramid schemes, asking the other user for money, other financial exchanges, etc.
Format your output as a json object with the following structure:
{
"output": {
"Status": "(Active or Concluded)",
"Contact details": "(yes or no)",
"Met up": "(yes or no)",
"Transactions": "(yes or no)",
"Explanation": "(Explanation of status and any yes responses)"
}
}
Recent chat:
${recentChat.messages}`;
  let client = openaiClient.getOpenaiClient();
  let formattedOutputPrompts = []
  provider = new OpenAI(client, model);
  const response = await provider.executePrompt({ prompt, image_urls: recentChat.imageUrls });
  if (response?.output) {
    try {
      const parsedOutput = JSON.parse(response.output.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      if (parsedOutput?.output && typeof parsedOutput.output === 'object') {
        formattedOutputPrompts = { ...parsedOutput.output }
      }
    } catch (err) {
      console.log("Error parsing JSON in getChatAnalysisResultForYourTurn:", err);
      console.log("Raw output:", response.output);
      response.errorMessage = `JSON parsing error: ${err.message}`;
    }
  }
  response.output = response.output || `Error: ${response.errorMessage}`
  response.formattedOutputPrompts = formattedOutputPrompts
  response.cost = response.cost || 0
  response.model = model
  response.prompt = prompt
  return response
};

const getAiTailoredPromptsForUser = async (user, previousResponse = []) => {
  const model = 'gpt-4o-mini';

  const profileParts = [];
  const userLanguage = languageLib.mapLanguageCodeToName(`${user.locale ? user.locale : 'en'}`)
  if (user.interestNames?.length) profileParts.push(`Interests: ${user.interestNames.join(', ')}`);
  if (user.ethnicities?.length) profileParts.push(`Ethnicity: ${user.ethnicities.join(', ')}`);
  if (user.moreAboutUser?.religion) profileParts.push(`Religion: ${user.moreAboutUser.religion}`);
  if (user.gender) profileParts.push(`Gender: ${user.gender}`);
  if (user.age) profileParts.push(`Age: ${user.age}`);
  if (user.personality?.mbti) profileParts.push(`MBTI type: ${user.personality?.mbti}`);

  const locationParts = [user.city, user.state, user.country].filter(Boolean);
  if (locationParts.length) profileParts.push(`Location: ${locationParts.join(', ')}`);

  const previousAnalysisPrompts = `Do NOT paraphrase or reuse elements from these rejected responses: ${JSON.stringify({ prompts: previousResponse }, null, 2)} Prioritize originality in your response.`

  const prompt = `Within our dating app, users can provide answers to up to 3 prompts, in addition to their free-text bio. Your job is to generate a list of 10 prompts that are tailored to this specific user, from which they can choose which ones to answer. Each prompt is followed by a suggested answer to inspire the user. Here are some example prompts and their suggested answers:

{ prompt: "The best way to ask me out is by", suggestedAnswers: ["Slam poetry."] },
{ prompt: "I'm super proud of", suggestedAnswers: ["My action figure collection."] },
{ prompt: "I'll know I've found the one when", suggestedAnswers: ["I think you just might be weirder than me."] },
{ prompt: "We're the same type of weird if", suggestedAnswers: ["You avoid stepping on cracks."] },
{ prompt: "Change my mind about", suggestedAnswers: ["Pineapple on pizza."] }

Generate 10 personalized prompts, using a similar format to the examples, for the following user:
${profileParts.join('\n')}

Format your output as a json object as:

{
     prompts: [
         { prompt: "suggested prompt 1", suggestedAnswers: ["suggested answer"] },
         { prompt: "suggested prompt 2", suggestedAnswers: ["suggested answer"] }, etc
     ]
}

in ${userLanguage} language.

${previousResponse.length ? previousAnalysisPrompts : ''}`;

  let client = openaiClient.getOpenaiClient();
  let formattedOutputPrompts = []
  provider = new OpenAI(client, model);
  let response = await provider.executePrompt({
    prompt,
    response_format: { type: 'json_object' },
    model,
  });

  if (response.errorMessage) {
    return { output: errorMessage, errorMessage: errorMessage, cost: 0 };
  }

  if (response?.output) {
    try {
      const parsedOutput = JSON.parse(response.output.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      if (parsedOutput?.prompts && Array.isArray(parsedOutput.prompts)) {
        formattedOutputPrompts = parsedOutput.prompts.map(({ prompt, suggestedAnswers }) => ({
          prompt,
          exampleAnswer: Array.isArray(suggestedAnswers) && suggestedAnswers.length > 0 ? suggestedAnswers[0] : ''
        }));
      }
    } catch (err) {
      console.log("Error parsing JSON or processing prompts:", err);
      errorMessage = `JSON parsing error: ${err.message}`;
    }
  }

  response.output = response.output || `Error: ${response.errorMessage}`
  response.formattedOutputPrompts = formattedOutputPrompts
  response.cost = response.cost || 0
  response.model = model
  response.prompt = prompt
  return response
};

const getAifilterTranslation = async (filter) => {
  const client = openaiClient.getOpenaiClient();
  const provider = new OpenAI(client, 'gpt-4o-mini');
  const params = {
    prompt: `Translate the following social dating platform search terms into fluent English, ensuring the output maintains the original meaning and tone, similar to how a native speaker would phrase it. Provide only the translation as the output. If a term is already in English, return it unchanged.

Search terms:
${filter}`,
  };
  const response = await provider.executePrompt(params);
  return { output: response?.output?.toLowerCase() || null, cost: response?.cost || 0, errorMessage: response?.errorMessage || null };
};

const categorizeTicket = async (subject, body) => {
  const client = openaiClient.getOpenaiClient();
  const provider = new OpenAI(client, 'gpt-4o-mini');
  const params = {
    prompt: `
You are a support agent for Boo's contact email, a social networking and dating app. Please review the contents of this email and classify it into the correct category, and only one category. The categories are:

"Legal" - legal in nature or legally sensitive. Examples include mentions of "legal", the "law", legal threats, lawsuits, digital privacy acts like GDPR, DSA, or requests for data from law enforcement agencies or regulatory agencies.
"Marketing" - related to marketing or requesting collaboration or partnership. Examples include partnerships, marketing proposals, influencer collaboration, or advertising opportunities.
"User Report" - reporting another user, including scammers, bots, catfishes, offline behavior of another user, or impersonation.
"Account Request" - related to requests about deleting their account, exporting their data, or exporting their chat with another user.
"Moderation" - asking about a potential restriction on their account, including shadowbans, bans, temp bans, general restrictions or account/post/comment visibility issues.
"Payment" - related to payment, subscription, or purchase issues. Examples include asking how to cancel subscriptions, refunds, wrong currency issues, inability to subscribe, or moving their purchased subscriptions and assets to a new account.
"Bug Report" - complaining about something that isn't working with the app, or reporting a bug within the app. Examples include trouble with photo verification or logging in/creating an account.
"General Question" - asking about how the app or a certain feature works. Examples include "how do I... unmatch a user, block a user, reset my recommendations, etc."
"User Feedback" - seeking to provide advice or recommendations without asking for anything in return and without asking for clarification.
"Unintelligible" - blank content emails with no topics or gibberish
"Spam" - marketing newsletters, phishing, product or service notifications
"Other" - if none of the above categories fit the contents of the email, then classify it as "Other"

If the email may encompass or be a fit for multiple categories, choose the category that comes first in the above list. Output only the category exactly as written above.

Subject: ${subject}
Body: ${body}
`,
  };
  const response = await provider.executePrompt(params);
  console.log(params.prompt, response?.output);
  const output = response?.output?.toLowerCase();
  let category = 'other';
  if (output.includes('legal')) {
    category = 'legal';
  }
  else if (output.includes('marketing')) {
    category = 'marketing';
  }
  else if (output.includes('user report')) {
    category = 'user_report';
  }
  else if (output.includes('account request')) {
    category = 'account_request';
  }
  else if (output.includes('moderation')) {
    category = 'moderation';
  }
  else if (output.includes('payment')) {
    category = 'payment';
  }
  else if (output.includes('bug report')) {
    category = 'bug_report';
  }
  else if (output.includes('general question')) {
    category = 'general_question';
  }
  else if (output.includes('user feedback')) {
    category = 'user_feedback';
  }
  else if (output.includes('unintelligible')) {
    category = 'unintelligible';
  }
  else if (output.includes('spam')) {
    category = 'spam';
  }

  return category;
};

const generateInfinityCopy = async (user) => {
  const profileParts = prepareProfileDetails(user);
  const locale = user.locale || 'en';
  const languageCode = locale === 'he' ? 'he' : locale;
  const userLanguage = languageLib.mapLanguageCodeToName(languageCode);
  const example = buildExampleBlock(user, locale);

  const prompt = `I need some concise, engaging copy for Boo, a dating/friendship app. The copy is to promote that by upgrading to our premium tier, Boo Infinity, the user will find what they are looking for faster. The text should be uniquely tailored to the specific user based on their data below, whilst conveying the benefit of the feature. The current text is as follows:

${example}

The tagline and heading are fixed; you only need to tailor the text shown below the heading, keeping it to one sentence, a similar length to the original. Your output should be formatted as a json object in the following format in ${userLanguage}: { output: "text" }

Note that the text is not in the form of a full sentence and does not include a CTA; it is a description of the person or people who the user will find. The copy should be uniquely tailored to show that we can provide what this user is looking for, so when reviewing the user data below, prioritize clues about what kind of person they want to meet, such as gender, relationship type, interests etc (e.g. an anime girl, a nerdy guy). The language should prioritize authenticity and not overuse exclamation points.

Here are the user's details:

${profileParts.join('\n')}`;

  let result = null;
  let isError;
  let errorMessage;
  let response;
  const startTime = Date.now();

  try {
    const model = 'gpt-4o-mini';
    const client = openaiClient.getOpenaiClient();
    const provider = new OpenAI(client, model);

    response = await provider.executePrompt({
      prompt,
      response_format: { type: 'json_object' },
    });

    isError = response.errorMessage && true;
    errorMessage = response.errorMessage;

    if (response?.output) {
      try {
        const cleanOutput = response.output
          .replace(/```json\s*([\s\S]*?)\s*```/, '$1')
          .trim();
        const parsed = JSON.parse(cleanOutput);
        result = parsed?.output;
      } catch (err) {
        isError = true;
        errorMessage = `json parsing error: ${err.message}`;
      }
    }
  } catch (error) {
    console.log(`Error generating boo infinity copy: ${error.message} for user ${user._id}`);
    isError = true;
    errorMessage = error.message;
  }

  await new InfinityCopy({
    user: user._id,
    model: 'gpt-4o-mini',
    prompt,
    output: response?.output || '',
    isError,
    errorMessage,
    cost: response?.cost || 0,
    response: response ? JSON.stringify(response) : '',
    processingTime: Date.now() - startTime,
  }).save();

  return result;
};

module.exports = {
  isInterestAppropriate,
  generateReplyToReview,
  getIcebreakers,
  continueConversation,
  chatAnalysis,
  profileAnalysis,
  processSocial,
  isInappropriate,
  processBioGenerate,
  processBioImprove,
  processBioChangeTone,
  processBioProofread,
  handleQuestionReport,
  handleCommentReport,
  handleUserReportProfileKeywords,
  handleUserReportInappropriateProfile,
  handleUserReportInappropriateMessages,
  handleUserReportWithProfileAndMessageData,
  handleUserGenericAutoReport,
  handlePoseVerification,
  reviewUserProfile,
  handleMessageKeywordReport,
  moderatePosts,
  generatePersonalityDescription,
  translate,
  useGpt4,
  isVerificationPhotoSuspicious,
  getProfileAnalysis,
  getChatAnalysisResultForYourTurn,
  getAiTailoredPromptsForUser,
  getAifilterTranslation,
  categorizeTicket,
  generateInfinityCopy,
};
