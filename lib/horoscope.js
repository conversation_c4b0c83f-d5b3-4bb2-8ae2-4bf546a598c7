const deepFreeze = require('deep-freeze');
const { getSign } = require('horoscope');
const momentTz = require('moment-timezone');
const moment = require('moment');

const horoscopes = [
  'Capricorn',
  'Aquarius',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Tau<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>ir<PERSON>',
  'Li<PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Sagittarius'
];
deepFreeze(horoscopes);

function getHoroscope(date) {
  return getSign(
    {
      month: date.getMonth() + 1, // In JS Date, 0 is Jan
      day: date.getDate(),
    },
    true,
  ); // if error, return null instead of throwing
}

function getAge(date, timezone) {
  if (!date) {
    return null;
  }
  if(timezone){
    const userTimezone = timezone;
    const userBirthdayInTimezone = momentTz.tz(date, userTimezone).startOf('day');
    const nowInUserTimezone = momentTz.tz(userTimezone).startOf('day');
    return nowInUserTimezone.diff(userBirthdayInTimezone, 'years');
  }else{
    return moment().diff(date, 'years');
  }
}

module.exports = {
  horoscopes,
  getHoroscope,
  getAge,
};
