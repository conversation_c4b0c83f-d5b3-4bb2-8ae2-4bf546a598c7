const moment = require('moment');
const moderatedKeywords = require('./moderated-keywords');
const reportLib = require('../lib/report');
const openai = require('../lib/openai');
const basicLib = require('../lib/basic');
const { getBannedInfringingText } = require('../lib/banned-infringing-text');
const Report = require('../models/report');

const SPLIT_KEYWORDS = [
  'fake account',
  'fake accounts',
  'fake profile',
  'fake profiles',
  'fake user',
  'fake users',
  'fake review',
  'fake reviews',
];

const BAN_KEYWORDS = [
  // scam-related
  'scammer',
  'scammers',
  'scam',
  'scams',
  'scammed',
  'scamming',
  'catfish',
  'catfishes',
  'catfished',
  'catfishing',
  'spam',
  'spammer',
  'spammers',
  'bot',
  'bots',
  'asking for money',
  'ask for money',
  'send me money',
  'send them money',

  // ban related
  'banned',
  'shadowbanned',
  'shadowban',
  'shadow ban',
  'shadow banned',
  'censorship',
  'censor',
  'censored',
  'censoring',
  'app store',
  'boo infinity',

  // swear words
  'fuck',
  'fucks',
  'fucking',
  'fucked',
  'fuckin',
  'fuking',
  'fukin',
  'crap',
  'crappy',
  'shit',
  'shitty',
  'shits',
  'bullshit',
  'suck',
  'sucks',
  'sucking',
  'nigger',
  'niggers',
  'fkn',
  'fk',
  'fck',
  'fckn',
  'asshole',
  'assholes',
  'ass hole',
  'ass holes',
  'ahole',
  'bitch',

  'delete this app',
  'deleted this app',
  'deleting this app',
  'delete the app',
  'deleted the app',
  'deleting the app',
  'uninstall this app',
  'uninstalled this app',
  'uninstalling this app',
  'uninstalls this app',
  'uninstall the app',
  'uninstalled the app',
  'uninstalling the app',
  'uninstalls the app',
  'delete account',
  'deleted account',
  'deleting account',
  'delete my account',
  'delete my boo account',
  'deleting boo',
  'delete boo',
  'deleted boo',
  'deletes boo',
  'inactive',

  // other competitors
  'birdy',
];

const SOCIAL_MEDIA_KEYWORDS = [
  'social media',
  'only fans',
  'onlyfans',
  'ig',
  'insta',
  'instagram',
  'snapchat',
  'snap chat',
  'snap',
  'sc',
  'discord',
  'facebook',
  'fb',
  'wechat',
  'we chat',
  'whatsapp',
  'whats app',
  'skype',
  'messenger',
  'kik',
  'viber',
  'google hangout',
  'google hangouts',
  'telegram',
  'follow me',
  'add me on',
  'add me at',
  'add me there',
  'add me here',
  'find me on',
  'find me at',
  'find me there',
  'find me here',
  "let's chat at",
  "let's chat on",
  "let's chat there",
  "let's chat here",
  'phone number',
  'phone #',
];

const COMPETITOR_KEYWORDS = [
  'sosyncd',
  'sosynced',
  'so syncd',
  'so synced',
  'ur my type',
  'urmytype',
  'umt',
  'typematch',
  'type match',
  'meetch',
];

const MONEY_KEYWORDS = [
  'paypal',
  'pay pal',
  'send me money',
  'cashapp',
  'cash app',
  'venmo',
  'pay me',
  'transfer money',
];

const DIRTY_KEYWORDS = [
  'penis',
  'penises',
  'cock',
  'cocks',
  'dic',
  'dick',
  'dicks',
  'pussy',
  'pussies',
  'vagina',
  'vaginas',
  'tit',
  'tits',
  'titty',
  'titties',
  'breast',
  'breasts',
  'nipple',
  'nipples',
  'puss',
  'cunt',
  'cunts',
  'choad',
  'choads',
  'milf',
  'milfs',
  'boner',
  'boners',
  'cockfoam',
  'cum',
  'cums',
  'cumming',
  'jizz',
  'jizzing',
  'boob',
  'boobs',
  'horny',
  'sugar mommy',
  'sugar mommies',
  'sugar momma',
  'sugar mommas',
  'sugar daddy',
  'sugar daddies',
  'hooker',
  'hookers',
  'prostitute',
  'prostitutes',
  'nude',
  'nudes',
];

const UNDERAGE_KEYWORDS = [
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '12y',
  '13y',
  '14y',
  '15y',
  '16y',
  '17y',
  '12yo',
  '13yo',
  '14yo',
  '15yo',
  '16yo',
  '17yo',
  'not 18',
  '7teen',
  '6teen',
];

const POST_BAN_KEYWORDS = [].concat(
  BAN_KEYWORDS,
  SOCIAL_MEDIA_KEYWORDS,
  COMPETITOR_KEYWORDS,
  MONEY_KEYWORDS,
  DIRTY_KEYWORDS,
  ['faggot'],
);

const ACCOUNT_REPORT_KEYWORDS = [].concat(
  DIRTY_KEYWORDS,
  UNDERAGE_KEYWORDS,
  ['sex', 'venta de contenido', 'buy pictures', 'adult content seller', 'contenido+18', 'i am minor', 'soy menor', '🍆', '💦', '🔞'],
);

const ACCOUNT_BAN_KEYWORDS = [].concat(
  MONEY_KEYWORDS,
  SOCIAL_MEDIA_KEYWORDS,
);

const MESSAGE_AUTO_HIDE_KEYWORDS = [].concat(
  MONEY_KEYWORDS,
  DIRTY_KEYWORDS,
  ['faggot'],
);

const COMPETITOR_URLS = [

  'sosyncd.com',
  'com.sosyncd',
  'so-syncd-personality-dating',

  'urmytype.app',
  'ur-my-type',

  'app-birdy.com',
  'com.birdyapp',
  'birdy-personality-matching',

  'typematchapp.com',
  'typematchapp',

  'meetch.app',
  'app.meetch',
  'meetch-meet-your-match',

];

const phoneNumberRegex = /\s*[0-9\(\)\-\+]([0-9\(\)\-\+\.\s]{5,20})[0-9]/g;
function findPhoneNumber(text) {
  const found = text.match(phoneNumberRegex);
  if (!found) {
    return null;
  }
  return found[0];
}

function findCompetitorUrl(text) {
  return COMPETITOR_URLS.find((s) => text.includes(s));
}

const emailRegex = /\s*\w+@\w+\.\w+/g;
function findEmail(text) {
  const found = text.match(emailRegex);
  if (!found) {
    return null;
  }
  return found[0];
}

const websiteRegex = /(https?:\/\/)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)?/gi;
function findWebsite(text) {
  const found = text.match(websiteRegex);
  if (!found) {
    return null;
  }
  return found[0];
}

function findSocialMediaHandle(text) {
  const re = /@\w+/;
  const found = text.match(re);
  if (!found) {
    return null;
  }
  return found[0];
}

function findBannedKeywordsHelper(text, keywords) {
  if (!text) {
    return false;
  }
  const lower = text.toLowerCase();
  /*
  we can store generated regex beforehand in const variable using for loop to avoid calling each time and creating new regexp instance
  for strings i.e.((s instanceof RegExp)?s:(new RegExp("\\b" + s + "\\b"))).test(lower)
  */
  return keywords.find((word) => basicLib.containsEntireWord(lower, word))
         || findPhoneNumber(lower)
         || findCompetitorUrl(lower);
}

function findBannedKeywordsHelperRegexOnly(text, keywords) {
  if (!text) {
    return false;
  }
  const lower = text.toLowerCase();
  return keywords.find((word) => basicLib.containsEntireWord(lower, word))
}

const languagesWithoutWordBoundaries = [
  'zh',
  'zh-Hans',
  'zh-Hant',
  'ja',
  'th',
  'my',
];

function containsBannedPostKeywords(text, locale) {
  if (!text) {
    return;
  }
  let keyword = findBannedKeywordsHelper(text, POST_BAN_KEYWORDS);
  if (keyword) {
    return keyword;
  }
  const lower = text.toLowerCase();
  if (moderatedKeywords[locale]) {
    if (languagesWithoutWordBoundaries.includes(locale)) {
      keyword = moderatedKeywords[locale].find((s) => lower.includes(s));
    } else {
      keyword = moderatedKeywords[locale].find((word) => basicLib.containsEntireWord(lower, word));
    }
  }
  if (keyword) {
    return keyword;
  }
  keyword = SPLIT_KEYWORDS.find((s) => s.split(' ').every((word) => basicLib.containsEntireWord(lower, word)));
  return keyword;
}

function removeBannedKeywords(text) {
  if (!text) {
    return text;
  }

  let result = text;

  // remove specific keywords
  for (const keyword of ACCOUNT_BAN_KEYWORDS) {
    const regex = new RegExp(`(^|[^\\p{L}\\p{N}])${keyword}(?=[^\\p{L}\\p{N}]|$)`, 'giu');
    result = result.replace(regex, '');
  }

  // remove phone numbers and emails
  result = result.replace(phoneNumberRegex,'');
  result = result.replace(emailRegex,'');

  // remove words containing @ or _
  for (const character of ['@', '_']) {
    const removeStr = `\\s?\\w*${character}\\w*`;
    const regex =  new RegExp(removeStr,'gi');
    result = result.replace(regex,'');
  }

  return result;
}

async function findBannedAccountKeywords(user) {
  if (user.shadowBanned && !user.profileTempBanReason) {
    // if the user is permanently banned, then no need to check
    return;
  }

  if (user.profileTempBanReason && user.bannedBy == 'openai') {
    // if user is banned by openai, check if ban can be lifted

    let priorReport;
    if (user.profileTempBanReportId) {
      priorReport = await Report.findById(user.profileTempBanReportId);
      if (priorReport) {
        // if user already attempted 10 fixes within 30 days, then ignore
        const priorAttempts = priorReport.openaiFollowUp.filter(x => x.date > moment().subtract(30, 'days').toDate());
        if (priorAttempts.length >= 10) {
          return;
        }
      }
    }

    const report = new Report();
    await openai.reviewUserProfile(user, report);
    if (priorReport) {
      priorReport.openaiFollowUp.push({
        date: Date.now(),
        ...report.openai,
      });
      await priorReport.save();
    }
    if (report.openai.ban == false) {
      await reportLib.undoProfileTempBan(user);
    }

    if (report.openai.ban == true && (report.openai.infringingText?.length || report.openai.infringingPictureKeys?.length)) {
      await reportLib.profileTempBan(user, report.openai.banReason, 'openai', report._id, report.openai.infringingText, report.openai.infringingPictureKeys, true);
    }
  }
  else {
    // otherwise check if user should be banned by openai

    // gather all profile fields
    let text = `${user.firstName || ''} ${user.education || ''} ${user.work || ''} ${user.description || ''}`;
    for (let i = 0; i < user.prompts.length; i++) {
      text = `${text} ${user.prompts[i].answer || ''}`;
    }

    {
      let keyword = findBannedKeywordsHelperRegexOnly(text, ACCOUNT_REPORT_KEYWORDS)
      if (keyword) {
        await reportLib.createReport(
          user,
          null,
          ['Auto-report due to profile keywords'],
          keyword,
          text,
        );
      }
    }
  }
}

function findBannedMessageKeywords(text) {
  return findBannedKeywordsHelperRegexOnly(text, MONEY_KEYWORDS)
}

function findAutoHideMessageKeywords(text) {
  return findBannedKeywordsHelperRegexOnly(text, MESSAGE_AUTO_HIDE_KEYWORDS)
}

function findBannedEarlyMessageKeywords(text) {
  const lower = text.toLowerCase();
  let keyword;
  keyword = findEmail(lower) || findPhoneNumber(lower);
  if (keyword) {
    return keyword;
  }
  if (lower.includes('@')) {
    return '@';
  }
  if (lower.includes('_')) {
    return '_';
  }
  return findBannedKeywordsHelperRegexOnly(text, SOCIAL_MEDIA_KEYWORDS);
}

const bannedEmailDomains = [
  'freesourcecodes.com',
  'freesourcecodez.online',
  'huracan.quest',
  'freerawonline.com',
  'gooners.world',
  'yyxcj.club',
  'nibley.me',
  'getaxemail.com',
  'mailngs.com',
  'ilovegooning.com',
  'mailelectric.com',
  'mailconstruction.com',
  'maildevelopment.com',
  'mailscommunity.com',
  'mailshoes.com',
  'ak.jakartam.com',
  'allfreemail.net',
  'allwebemails.com',
  'anymorejk.com',
  'as.grandmada.org',
  'az.haphazardo.com',
  'bingessong.org',
  'bkle.uk',
  'bn.chenjiagyt.org',
  'bns.bnsm.life',
  'ce.knockeduy.org',
  'ci.medicinexx.org',
  'cv.singaporpe.org',
  'dq.dknkdq.life',
  'dy.inquirydg.com',
  'e.commentsf.com',
  'e.fewne.life',
  'edny.net',
  'eoqjjqg.com',
  'erl.togetherl.run',
  'europhean.com',
  'everydaysk.org',
  'facai168.asia',
  'fg.quenchedk.org',
  'fmailnex.com',
  'ga.singaporpe.org',
  'gp.rndrm.com',
  'grandpabv.com',
  'gt.chenjiagyt.org',
  'inboxorigin.com',
  'inquirydg.com',
  'ir.inquirydg.com',
  'j.zjfzjzd.org',
  'jg.xianyuajp.org',
  'jianvan.com',
  'joanbingcv.org',
  'joggingxz.com',
  'ko.korgmz.com',
  'ku.brandnewph.com',
  'lp.gettinglp.com',
  'ly.krystalyte.org',
  'ma.grandmada.org',
  'mm.medicinexx.org',
  'ncps.cc',
  'nf.gardensdfg.com',
  'ni.medicinei.com',
  'nqmo.com',
  'nt.innocentk.com',
  'ok.gorgeousk.org',
  'overcomekp.org',
  'pn.happenedj.com',
  'qq.butingquan.com',
  'r.korgmz.com',
  'rr.gorgeousk.org',
  'se.becauseyo.com',
  'sidbasis.com',
  'singaporpe.org',
  'sk.gorgeousk.org',
  'so.bingessong.org',
  'ta.krystalyte.org',
  'tf.commentsf.com',
  'tr.startedkd.com',
  'ts.everydaysk.org',
  'uk.aizle.uk',
  'us.youngesta.org',
  'usernamek.org',
  'voucherqpo.org',
  'vv.rndrm.com',
  'yazhai.co',
];

module.exports = {
  UNDERAGE_KEYWORDS,
  findBannedAccountKeywords,
  containsBannedPostKeywords,
  findBannedMessageKeywords,
  findBannedEarlyMessageKeywords,
  findAutoHideMessageKeywords,
  removeBannedKeywords,
  bannedEmailDomains,
};
